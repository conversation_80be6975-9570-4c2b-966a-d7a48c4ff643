{% extends "base.html" %}
{% load static %}

{% block title %}高级选股筛选器 - 股票数据分析系统{% endblock %}

{% block extra_css %}
<style>
.filter-section {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.filter-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    cursor: pointer;
}

.filter-body {
    padding: 1rem;
    background: #f8f9fa;
}

.preset-card {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preset-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.1);
}

.preset-card.active {
    border-color: #007bff;
    background: #f0f8ff;
}

.range-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.range-input input {
    flex: 1;
}

.filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.filter-tag {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-tag .remove {
    cursor: pointer;
    font-weight: bold;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">
                    <i class="ti ti-filter me-2"></i>高级选股筛选器
                </h2>
                <div class="page-subtitle">
                    多维度股票筛选，助您发现投资机会
                </div>
            </div>
            <div class="col-auto">
                <div class="btn-list">
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#presetModal">
                        <i class="ti ti-template me-1"></i>预设策略
                    </button>
                    <button type="button" class="btn btn-primary" onclick="applyFilters()">
                        <i class="ti ti-search me-1"></i>开始筛选
                    </button>
                </div>
            </div>
        </div>
    </div>

    <form method="get" id="screenerForm">
        <div class="row">
            <!-- 筛选条件面板 -->
            <div class="col-lg-3">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">筛选条件</h3>
                        <div class="card-actions">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearFilters()">
                                清空条件
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- 基本筛选 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#basicFilters">
                                <i class="ti ti-building me-2"></i>基本筛选
                            </div>
                            <div class="collapse show filter-body" id="basicFilters">
                                <div class="mb-3">
                                    <label class="form-label">行业</label>
                                    <select name="industry" class="form-select">
                                        <option value="">全部行业</option>
                                        {% for industry in industries %}
                                        <option value="{{ industry }}" {% if industry == filters.industry %}selected{% endif %}>
                                            {{ industry }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">市值范围(亿元)</label>
                                    <div class="range-input">
                                        <input type="number" name="market_cap_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.market_cap_min }}">
                                        <span>-</span>
                                        <input type="number" name="market_cap_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.market_cap_max }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 估值指标 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#valuationFilters">
                                <i class="ti ti-chart-line me-2"></i>估值指标
                            </div>
                            <div class="collapse filter-body" id="valuationFilters">
                                <div class="mb-3">
                                    <label class="form-label">市盈率(PE)</label>
                                    <div class="range-input">
                                        <input type="number" name="pe_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.pe_min }}">
                                        <span>-</span>
                                        <input type="number" name="pe_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.pe_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">市净率(PB)</label>
                                    <div class="range-input">
                                        <input type="number" name="pb_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.pb_min }}">
                                        <span>-</span>
                                        <input type="number" name="pb_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.pb_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">PEG比率</label>
                                    <div class="range-input">
                                        <input type="number" name="peg_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.peg_min }}">
                                        <span>-</span>
                                        <input type="number" name="peg_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.peg_max }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 盈利能力 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#profitabilityFilters">
                                <i class="ti ti-trending-up me-2"></i>盈利能力
                            </div>
                            <div class="collapse filter-body" id="profitabilityFilters">
                                <div class="mb-3">
                                    <label class="form-label">ROE(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="roe_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.roe_min }}">
                                        <span>-</span>
                                        <input type="number" name="roe_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.roe_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">ROA(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="roa_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.roa_min }}">
                                        <span>-</span>
                                        <input type="number" name="roa_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.roa_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">净利率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="net_profit_margin_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.net_profit_margin_min }}">
                                        <span>-</span>
                                        <input type="number" name="net_profit_margin_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.net_profit_margin_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">毛利率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="gross_profit_margin_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.gross_profit_margin_min }}">
                                        <span>-</span>
                                        <input type="number" name="gross_profit_margin_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.gross_profit_margin_max }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 成长性指标 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#growthFilters">
                                <i class="ti ti-rocket me-2"></i>成长性指标
                            </div>
                            <div class="collapse filter-body" id="growthFilters">
                                <div class="mb-3">
                                    <label class="form-label">营收增长率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="revenue_growth_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.revenue_growth_min }}">
                                        <span>-</span>
                                        <input type="number" name="revenue_growth_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.revenue_growth_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">净利润增长率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="profit_growth_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.profit_growth_min }}">
                                        <span>-</span>
                                        <input type="number" name="profit_growth_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.profit_growth_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">3年营收复合增长率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="revenue_cagr_3y_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.revenue_cagr_3y_min }}">
                                        <span>-</span>
                                        <input type="number" name="revenue_cagr_3y_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.revenue_cagr_3y_max }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 财务健康度 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#healthFilters">
                                <i class="ti ti-shield-check me-2"></i>财务健康度
                            </div>
                            <div class="collapse filter-body" id="healthFilters">
                                <div class="mb-3">
                                    <label class="form-label">资产负债率(%)</label>
                                    <div class="range-input">
                                        <input type="number" name="debt_ratio_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.debt_ratio_min }}">
                                        <span>-</span>
                                        <input type="number" name="debt_ratio_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.debt_ratio_max }}">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">流动比率</label>
                                    <div class="range-input">
                                        <input type="number" name="current_ratio_min" class="form-control" 
                                               placeholder="最小值" value="{{ filters.current_ratio_min }}">
                                        <span>-</span>
                                        <input type="number" name="current_ratio_max" class="form-control" 
                                               placeholder="最大值" value="{{ filters.current_ratio_max }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 特殊筛选 -->
                        <div class="filter-section">
                            <div class="filter-header" data-bs-toggle="collapse" data-bs-target="#specialFilters">
                                <i class="ti ti-star me-2"></i>特殊筛选
                            </div>
                            <div class="collapse filter-body" id="specialFilters">
                                <div class="mb-3">
                                    <label class="form-label">连续ROE>15%年数</label>
                                    <select name="continuous_roe_years" class="form-select">
                                        <option value="">不限制</option>
                                        <option value="3" {% if filters.continuous_roe_years == "3" %}selected{% endif %}>3年</option>
                                        <option value="5" {% if filters.continuous_roe_years == "5" %}selected{% endif %}>5年</option>
                                        <option value="10" {% if filters.continuous_roe_years == "10" %}selected{% endif %}>10年</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">连续增长年数</label>
                                    <select name="continuous_growth_years" class="form-select">
                                        <option value="">不限制</option>
                                        <option value="3" {% if filters.continuous_growth_years == "3" %}selected{% endif %}>3年</option>
                                        <option value="5" {% if filters.continuous_growth_years == "5" %}selected{% endif %}>5年</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 结果展示面板 -->
            <div class="col-lg-9">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            筛选结果 
                            <span class="badge bg-primary ms-2">{{ total_count }}</span>
                        </h3>
                        <div class="card-actions">
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    排序方式
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="#" onclick="sortBy('market_cap', 'desc')">市值从大到小</a>
                                    <a class="dropdown-item" href="#" onclick="sortBy('market_cap', 'asc')">市值从小到大</a>
                                    <a class="dropdown-item" href="#" onclick="sortBy('roe', 'desc')">ROE从高到低</a>
                                    <a class="dropdown-item" href="#" onclick="sortBy('pe_ratio', 'asc')">PE从低到高</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        {% if stocks %}
                        <div class="table-responsive">
                            <table class="table table-vcenter">
                                <thead>
                                    <tr>
                                        <th>操作</th>
                                        <th>股票代码</th>
                                        <th>股票名称</th>
                                        <th>行业</th>
                                        <th>市值(亿)</th>
                                        <th>PE</th>
                                        <th>PB</th>
                                        <th>ROE(%)</th>
                                        <th>营收增长(%)</th>
                                        <th>净利增长(%)</th>
                                        <th>负债率(%)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stock in stocks %}
                                    <tr>
                                        <td>
                                            <button class="btn btn-sm btn-warning favorite-btn" 
                                                    data-code="{{ stock.stock_code }}" 
                                                    data-name="{{ stock.stock_name }}">
                                                <i class="ti ti-star"></i>
                                            </button>
                                        </td>
                                        <td>
                                            <a href="{% url 'market_data:stock_detail' stock.stock_code %}" 
                                               class="text-primary fw-bold">
                                                {{ stock.stock_code }}
                                            </a>
                                        </td>
                                        <td>{{ stock.stock_name }}</td>
                                        <td>
                                            <span class="badge bg-light text-dark">{{ stock.industry }}</span>
                                        </td>
                                        <td>{{ stock.market_cap|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.pe_ratio|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.pb_ratio|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.roe|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.revenue_growth|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.profit_growth|floatformat:1|default:"-" }}</td>
                                        <td>{{ stock.debt_ratio|floatformat:1|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        {% if page_obj.has_other_pages %}
                        <div class="card-footer">
                            {% include 'includes/pagination.html' %}
                        </div>
                        {% endif %}
                        
                        {% else %}
                        <div class="empty">
                            <div class="empty-icon">
                                <i class="ti ti-search"></i>
                            </div>
                            <p class="empty-title">暂无符合条件的股票</p>
                            <p class="empty-subtitle text-muted">
                                请调整筛选条件后重新搜索
                            </p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- 预设策略模态框 -->
<div class="modal modal-blur fade" id="presetModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择预设筛选策略</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="preset-card" data-preset="value_stocks">
                            <h6>价值股筛选</h6>
                            <p class="text-muted">低估值、高分红、财务稳健</p>
                            <div class="small text-muted">
                                PE≤15, PB≤2, ROE≥10%, 负债率≤50%, 股息率≥3%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="preset-card" data-preset="growth_stocks">
                            <h6>成长股筛选</h6>
                            <p class="text-muted">高成长、高ROE</p>
                            <div class="small text-muted">
                                营收增长≥20%, 净利增长≥25%, ROE≥15%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="preset-card" data-preset="quality_stocks">
                            <h6>优质股筛选</h6>
                            <p class="text-muted">连续盈利、ROE稳定</p>
                            <div class="small text-muted">
                                连续3年ROE>15%, 负债率≤40%, 流动比率≥1.5
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="preset-card" data-preset="dividend_stocks">
                            <h6>分红股筛选</h6>
                            <p class="text-muted">高股息率、连续分红</p>
                            <div class="small text-muted">
                                股息率≥4%, 连续分红≥5年, ROE≥8%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="applyPreset()">应用策略</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let selectedPreset = null;

// 应用筛选条件
function applyFilters() {
    document.getElementById('screenerForm').submit();
}

// 清空筛选条件
function clearFilters() {
    document.getElementById('screenerForm').reset();
    window.location.href = window.location.pathname;
}

// 排序
function sortBy(field, order) {
    const form = document.getElementById('screenerForm');
    const sortByInput = document.createElement('input');
    sortByInput.type = 'hidden';
    sortByInput.name = 'sort_by';
    sortByInput.value = field;
    
    const sortOrderInput = document.createElement('input');
    sortOrderInput.type = 'hidden';
    sortOrderInput.name = 'sort_order';
    sortOrderInput.value = order;
    
    form.appendChild(sortByInput);
    form.appendChild(sortOrderInput);
    form.submit();
}

// 预设策略选择
document.querySelectorAll('.preset-card').forEach(card => {
    card.addEventListener('click', function() {
        document.querySelectorAll('.preset-card').forEach(c => c.classList.remove('active'));
        this.classList.add('active');
        selectedPreset = this.dataset.preset;
    });
});

// 应用预设策略
function applyPreset() {
    if (!selectedPreset) return;
    
    const presets = {
        'value_stocks': {
            'pe_max': 15,
            'pb_max': 2,
            'roe_min': 10,
            'debt_ratio_max': 50,
            'dividend_yield_min': 3
        },
        'growth_stocks': {
            'revenue_growth_min': 20,
            'profit_growth_min': 25,
            'roe_min': 15
        },
        'quality_stocks': {
            'continuous_roe_years': 3,
            'roe_min': 15,
            'debt_ratio_max': 40,
            'current_ratio_min': 1.5
        },
        'dividend_stocks': {
            'dividend_yield_min': 4,
            'dividend_years_min': 5,
            'roe_min': 8
        }
    };
    
    const preset = presets[selectedPreset];
    if (preset) {
        // 清空现有条件
        document.getElementById('screenerForm').reset();
        
        // 应用预设条件
        for (const [key, value] of Object.entries(preset)) {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = value;
            }
        }
        
        // 关闭模态框并提交表单
        const modal = bootstrap.Modal.getInstance(document.getElementById('presetModal'));
        modal.hide();
        
        setTimeout(() => {
            applyFilters();
        }, 300);
    }
}

// 收藏功能
document.querySelectorAll('.favorite-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const stockCode = this.dataset.code;
        const stockName = this.dataset.name;
        
        fetch(`{% url 'financial_analysis:ajax_add_favorite' %}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                stock_code: stockCode,
                stock_name: stockName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.classList.remove('btn-warning');
                this.classList.add('btn-success');
                this.disabled = true;
                this.innerHTML = '<i class="ti ti-check"></i>';
            }
        });
    });
});
</script>
{% endblock %}
