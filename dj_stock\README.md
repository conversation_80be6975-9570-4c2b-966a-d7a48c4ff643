# 股票数据分析系统 (Stock Data Analysis System)

这是一个基于 Django 5.1.7 开发的股票数据分析系统，提供股票行情、财务数据分析、资金流向分析、活跃营业部和涨跌停数据等功能。系统采用 Tabler UI 框架设计，支持数据可视化展示，并具有智能数据更新机制。

## 项目概述

本系统是一个全面的股票数据分析平台，集成了市场数据采集、财务分析、技术指标计算、资金流向追踪等多个功能模块。系统通过 akshare 数据接口获取实时和历史股票数据，为投资者和分析师提供专业的数据分析工具。

### 核心特性

- 🚀 **实时数据更新**: 支持实时股票行情和财务数据更新
- 📊 **数据可视化**: 基于 ECharts 的专业图表展示
- 🔍 **智能筛选**: 多维度财务指标筛选和股票筛选
- 📱 **响应式设计**: 支持桌面端和移动端访问
- 🔐 **用户系统**: 完整的用户认证和个人收藏功能
- ⚡ **高性能**: 数据库优化和缓存机制确保快速响应

## 主要功能

### 市场数据模块 (market_data)

- **股票基础信息**: 股票代码、名称、行业分类等基本信息
- **行业板块分析**: 行业板块排行、成分股分析、板块资金流向
- **概念板块分析**: 概念板块数据、热点概念追踪
- **资金流向分析**: 个股资金流向、板块资金流向、市场整体资金流向
- **涨跌停数据**: 涨停股票、跌停股票、一字板统计
- **活跃营业部**: 营业部交易排行、龙虎榜数据
- **市场指数**: 主要指数行情、指数成分股
- **IPO数据**: 新股发行、上市时间表
- **融资融券**: 融资融券余额、标的股票
- **港股通数据**: 沪深港通资金流向
- **风险警示**: ST股票、退市风险股票

### 财务分析模块 (financial_analysis)

- **财务指标分析**: 营收、净利润、ROE、ROA等核心财务指标
- **财务报表筛选**: 多维度财务数据筛选和排序
- **分红派息**: 股票分红历史、除权除息日期
- **股东信息**: 股东持股变化、机构持股统计
- **行业PE分析**: 行业市盈率对比、估值分析
- **技术指标**: 技术分析指标计算和展示
- **产业链分析**: 上下游产业链关系图谱

## 功能特点

### 市场数据

- 股票基本信息查询 - 支持多维度筛选和排序
- 实时行情数据展示 - 实时更新股票价格和涨跌幅
- 历史行情数据查询 - 支持K线图和技术指标
- 行业板块分析 - 行业涨跌幅排行和成分股分析
- 概念板块分析 - 热点概念跟踪和成分股分析
- 新股上市信息 - IPO申购和上市信息查询
- 市场指数数据 - 上证、深证、创业板等主要指数
- 风险预警股票监控 - ST和*ST股票预警
- 数据统计与可视化 - 市场概览和PE分布
- 行业产业链分析 - 产业链上下游关系展示
- 龙虎榜数据分析 - 机构交易行为分析
- 千股千评数据 - 机构评级和目标价格查询
- 涨跌停数据分析 - 每日涨跌停股票统计和分析
- 资金流向分析 - 市场、行业和个股资金流向分析
- 活跃营业部分析 - 营业部买卖行为和买卖股票分析

### 财务分析

- 财务指标数据采集与展示
  - 从2015年至今的完整财务数据
  - 智能判断数据更新时机，90天自动检查
  - 关键财务指标可视化展示
  - 财务数据对比分析
- 分红派息记录查询 - 历史分红数据和统计
- 财务报表分析 - 资产负债表、利润表、现金流量表
- 行业财务对比 - 同行业公司财务指标对比
- 股东统计分析 - 股东结构和变动分析
- 技术指标分析 - 常用技术指标计算与展示
- 行业PE分布 - 行业估值水平分析

## 技术特点

## 技术栈

### 后端技术

- **Web框架**: Django 5.1.7
- **数据库**: MySQL (mysqlclient 2.2.7)
- **数据源**: akshare 1.16.65 (股票数据API)
- **数据处理**: pandas 2.2.3, numpy 2.2.4
- **异步处理**: aiohttp 3.11.14
- **网络请求**: requests 2.32.3
- **数据解析**: beautifulsoup4 4.13.3, lxml 5.3.1

### 前端技术

- **UI框架**: Tabler UI (基于Bootstrap)
- **图表库**: ECharts
- **JavaScript**: 原生JavaScript + jQuery
- **CSS**: Bootstrap + 自定义样式

### 开发工具

- **Python版本**: 3.12+
- **包管理**: pip + requirements.txt / pyproject.toml
- **数据库工具**: Django ORM + 原生SQL
- **调试工具**: Django Debug Toolbar
- **日志系统**: Python logging

### 数据采集

- 使用 akshare 和 Tushare 接口获取数据
- 支持增量更新，避免重复采集
- 智能判断数据更新时机（90天自动检查）
- 完善的错误处理和重试机制
- 数据采集状态监控和日志记录

### 数据展示

- 响应式布局设计，支持移动端访问
- 数据可视化展示，支持多种图表类型
- 灵活的数据筛选和排序功能
- 标准化分页组件，支持自定义每页记录数
- 统一的数据表格和表单设计

### 性能优化

- 数据库查询优化，使用索引和复合索引
- 数据库连接池管理
- 数据缓存机制，减少重复计算
- 异步数据加载，提升用户体验
- 分页性能优化，支持大数据量处理
- 数据库查询结果缓存

## 系统架构

### 应用模块

#### market_data（市场数据模块）

**URL前缀**: `/market_data/`

**主要功能页面**:

- **首页** (`/`) - 市场概览、主要指数、涨跌幅榜单
- **股票管理**
  - 股票列表 (`/stocks/`) - 支持多条件筛选和排序
  - 股票详情 (`/stocks/<code>/`) - 基本信息、K线图、财务指标
  - 股票历史 (`/stock-history/<code>/`) - 历史行情数据查询
- **板块分析**
  - 行业板块列表 (`/industry-board/`) - 行业涨跌幅排行
  - 行业板块详情 (`/industry-board/<code>/`) - 成分股查询
  - 概念板块列表 (`/concept-board/`) - 概念板块涨跌幅排行
  - 概念板块详情 (`/concept-board/<code>/`) - 成分股查询
  - 产业链列表 (`/industry-chain/`) - 产业链数据展示
- **市场监控**
  - 风险预警 (`/risk-warning/`) - ST和*ST股票监控
  - 新股上市 (`/ipo-list/`) - IPO信息查询
  - 涨跌停列表 (`/limit-list/`) - 每日涨跌停股票统计
- **指数数据**
  - 市场指数列表 (`/market-index/`) - 主要指数行情
  - 市场指数详情 (`/market-index/<code>/`) - 指数历史数据
- **资金流向**
  - 市场资金流向 (`/market-fund-flow/`) - 市场整体资金流向
  - 行业资金流向 (`/industry-fund-flow/`) - 各行业资金流向
  - 个股资金流向 (`/stock-fund-flow/`) - 个股资金流向数据
  - 个股资金流向详情 (`/stock-fund-flow/<code>/`) - 单只股票资金流向历史
- **交易数据**
  - 龙虎榜列表 (`/dragon-tiger/`) - 龙虎榜交易数据
  - 龙虎榜详情 (`/dragon-tiger/<code>/`) - 单只股票龙虎榜记录
  - 活跃营业部列表 (`/active-broker/`) - 活跃营业部交易数据
  - 活跃营业部详情 (`/active-broker/<code>/`) - 单个营业部交易记录
- **其他数据**
  - 千股千评列表 (`/stock-comment/`) - 机构评级和目标价格
  - 千股千评详情 (`/stock-comment/<code>/`) - 单只股票评级历史
  - 融资融券列表 (`/margin-trading/`) - 融资融券数据概览
  - 融资融券详情 (`/margin-trading/<code>/`) - 单只股票融资融券数据
  - 港股通数据 (`/hk-connect/`) - 港股通持股和成交数据
  - 数据统计 (`/data-statistics/`) - 市场统计和可视化

#### financial_analysis（财务分析模块）

**URL前缀**: `/financial-analysis/`

**主要功能页面**:

- **财务数据**
  - 财务报表列表 (`/financial-report/`) - 财务报表数据查询
  - 财务指标详情 (`/financial-indicator/<code>/`) - 单只股票财务指标
  - 财务筛选器 (`/financial-screener/`) - 多维度财务指标筛选
- **分红数据**
  - 分红记录 (`/dividend/`) - 历史分红派息查询
- **股东信息**
  - 股东统计 (`/shareholder-statistics/<code>/`) - 股东结构和变动分析
  - 股东详情列表 (`/shareholder-detail/`) - 主要股东持股变动
  - 股东详情 (`/shareholder-detail/<code>/`) - 单只股票股东详情
- **技术分析**
  - 技术指标列表 (`/technical-indicators/`) - 技术指标计算与展示
  - 技术指标详情 (`/technical-indicators/<pk>/`) - 单个技术指标详情
- **行业分析**
  - 行业PE列表 (`/industry-pe/`) - 行业估值水平分析
  - 产业链列表 (`/industry-chains/`) - 产业链关系图谱
  - 产业链详情 (`/industry-chains/<chain_code>/`) - 产业链详细信息
- **用户功能**
  - 股票收藏 (`/favorites/`) - 用户收藏的股票列表
  - 批量收藏 (`/batch-favorite/`) - 批量添加收藏
  - AJAX收藏 (`/ajax-add-favorite/`) - 异步添加收藏

## 关键指标展示

### 财务指标

- 行业分布统计 - 各行业股票数量和市值占比
- 市场成交量趋势 - 市场成交量和成交额变化
- 行业板块涨跌幅分布 - 各行业涨跌幅对比
- 概念板块涨跌幅分布 - 各概念涨跌幅对比
- PE分布统计 - 市场估值水平分布
- 风险预警股票监控 - ST和*ST股票列表
- 涨跌停股票统计 - 每日涨跌停数量和占比
- 市场指数走势对比 - 上证、深证、创业板指数对比
- 成交量和成交额分析 - 市场活跃度分析
- 资金流向分析 - 市场、行业和个股资金流向
- 活跃营业部分析 - 营业部交易行为分析

## 数据更新策略

### 财务数据

- 定期更新：每季度财报公布后自动更新
- 智能检测：系统每90天自动检测最新财报状态
- 增量更新：只更新新增的财务数据，减少系统负担
- 错误重试：当数据更新失败时自动重试

### 行情数据

- 实时更新：交易时段实时获取行情数据
- 定时更新：每日收盘后自动更新当日历史数据
- 历史数据：支持一次性获取历史行情数据

### 板块数据

- 定期更新：每周自动更新行业和概念板块成分股
- 实时行情：交易时段实时获取板块涨跌幅数据

### 资金流向数据

- 定时更新：每日收盘后自动更新当日资金流向数据
- 历史数据：支持一次性获取历史资金流向数据

### 活跃营业部数据

- 定时更新：每日收盘后自动更新当日活跃营业部数据
- 历史数据：支持一次性获取历史活跃营业部数据

## 安装与使用

### 环境要求

- **Python**: 3.12+ (推荐使用最新版本)
- **Django**: 5.1.7+
- **数据库**: MySQL 5.7+ 或 MySQL 8.0+
- **内存**: 建议4GB以上
- **存储**: 建议10GB以上可用空间
- **网络**: 需要访问股票数据API接口

### 依赖包版本

主要依赖包及版本（详见 requirements.txt 和 pyproject.toml）：

```
Django>=5.1.7
akshare>=1.16.65
pandas>=2.2.3
numpy>=2.2.4
mysqlclient>=2.2.7
aiohttp>=3.11.14
requests>=2.32.3
beautifulsoup4>=4.13.3
lxml>=5.3.1
```

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/yourusername/dj_stock.git
cd dj_stock
```

2. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate  # Windows
```

3. 安装依赖

```bash
pip install -r requirements.txt
```

4. 配置数据库

编辑 `dj_stock/settings.py` 文件中的数据库配置：

```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "stock_data",
        "USER": "your_username",
        "PASSWORD": "your_password",
        "HOST": "localhost",
        "PORT": "3306",
        "OPTIONS": {
            "charset": "utf8mb4",
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

5. 创建日志目录

```bash
mkdir -p logs
```

6. 运行数据库迁移

```bash
python manage.py makemigrations
python manage.py migrate
```

7. 创建超级管理员

```bash
python manage.py createsuperuser
```

8. 启动开发服务器

```bash
python manage.py runserver
```

9. 访问系统

```
浏览器访问: http://127.0.0.1:8000
管理后台: http://127.0.0.1:8000/admin
```

### 数据初始化

首次使用需要初始化数据：

```bash
# 初始化股票列表（如果有相应的管理命令）
python manage.py init_stocks

# 初始化行业和概念板块（如果有相应的管理命令）
python manage.py init_sectors

# 获取历史行情数据（如果有相应的管理命令）
python manage.py fetch_history_data

# 获取财务数据（如果有相应的管理命令）
python manage.py fetch_financial_data
```

### 开发工具

项目提供了多个开发和维护工具：

```bash
# 检查财务指标
python check_indicators.py

# 检查指标映射匹配情况
python check_indicators_match.py

# 检查数据编码问题
python check_data_encoding.py

# 批量刷新股票数据
python refresh_stock_data.py
```

详细的工具说明请参考 [TOOLS_README.md](TOOLS_README.md)。

## 项目结构

```
dj_stock/
├── dj_stock/                    # Django项目配置
│   ├── __init__.py
│   ├── settings.py              # 项目设置(数据库、中间件、应用配置)
│   ├── urls.py                  # 主URL路由配置
│   ├── wsgi.py                  # WSGI配置
│   └── asgi.py                  # ASGI配置
├── market_data/                 # 市场数据应用
│   ├── __init__.py
│   ├── models.py                # 数据模型(股票基础、板块、资金流向等)
│   ├── admin.py                 # Django管理后台配置
│   ├── apps.py                  # 应用配置
│   ├── views/                   # 视图模块
│   │   ├── __init__.py
│   │   ├── stock_views.py       # 股票相关视图
│   │   ├── board_views.py       # 板块相关视图
│   │   ├── fund_flow_views.py   # 资金流向视图
│   │   ├── broker_views.py      # 营业部视图
│   │   ├── index_views.py       # 指数视图
│   │   └── views.py             # 其他视图
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   ├── stock_fetcher.py     # 股票数据获取
│   │   └── pagination.py       # 分页工具
│   ├── migrations/              # 数据库迁移文件
│   ├── urls.py                  # URL配置
│   └── tests.py                 # 测试文件
├── financial_analysis/          # 财务分析应用
│   ├── __init__.py
│   ├── models.py                # 财务数据模型
│   ├── admin.py                 # Django管理后台配置
│   ├── apps.py                  # 应用配置
│   ├── views/                   # 视图模块
│   │   ├── __init__.py
│   │   ├── views.py             # 主要视图
│   │   └── shareholder_views.py # 股东视图
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   └── financial_fetcher.py # 财务数据获取
│   ├── migrations/              # 数据库迁移文件
│   ├── urls.py                  # URL配置
│   └── tests.py                 # 测试文件
├── static/                      # 静态资源
│   ├── css/                     # CSS样式文件
│   │   ├── tabler.min.css       # Tabler UI框架
│   │   └── custom.css           # 自定义样式
│   ├── js/                      # JavaScript脚本
│   │   ├── tabler.min.js        # Tabler UI脚本
│   │   ├── echarts.min.js       # ECharts图表库
│   │   └── custom.js            # 自定义脚本
│   └── img/                     # 图片资源
├── templates/                   # 模板文件
│   ├── base.html                # 基础模板
│   ├── login.html               # 登录页面
│   ├── login_base.html          # 登录基础模板
│   ├── data_statistics.html     # 数据统计页面
│   ├── market_data/             # 市场数据模板
│   │   ├── index.html           # 首页
│   │   ├── stock_list.html      # 股票列表
│   │   ├── stock_detail.html    # 股票详情
│   │   └── ...                  # 其他模板
│   ├── financial_analysis/      # 财务分析模板
│   │   ├── financial_report_list.html    # 财务报表列表
│   │   ├── financial_indicator.html      # 财务指标
│   │   ├── financial_screener.html       # 财务筛选器
│   │   └── ...                           # 其他模板
│   ├── includes/                # 公共组件模板
│   │   ├── pagination.html      # 分页组件
│   │   ├── navbar.html          # 导航栏
│   │   └── ...                  # 其他组件
│   └── components/              # 可复用组件
├── logs/                        # 日志文件
│   ├── django_debug.log         # Django调试日志
│   └── financial_analysis.log   # 财务分析日志
├── test_reports/                # 测试报告
├── requirements.txt             # 依赖包列表
├── pyproject.toml               # 项目配置文件
├── manage.py                    # Django管理脚本
├── stock.py                     # 股票数据处理脚本
├── refresh_stock_data.py        # 股票数据刷新工具
├── check_indicators.py          # 财务指标检查工具
├── check_indicators_match.py    # 指标映射检查工具
├── check_data_encoding.py       # 数据编码检查工具
├── TOOLS_README.md              # 开发工具说明
└── README.md                    # 项目说明文档
```

## 数据库设计

### 主要数据表

#### market_data 应用

- `stock_basic` - 股票基础信息
- `stock_daily_quote` - 股票日行情数据
- `industry_board` - 行业板块数据
- `concept_board` - 概念板块数据
- `board_stock` - 板块成分股关系
- `stock_fund_flow` - 个股资金流向
- `industry_fund_flow` - 行业资金流向
- `market_fund_flow` - 市场资金流向
- `stock_limit_list` - 涨跌停数据
- `active_broker` - 活跃营业部数据
- `dragon_tiger_list` - 龙虎榜数据
- `stock_comment` - 千股千评数据
- `margin_trading` - 融资融券数据
- `hk_connect` - 港股通数据
- `market_index` - 市场指数数据
- `ipo_info` - IPO信息

#### financial_analysis 应用

- `stock_financial_indicator` - 股票财务指标（核心表）
- `stock_dividend` - 股票分红数据
- `stock_shareholder_statistics` - 股东统计数据
- `stock_shareholder_detail` - 股东持股明细
- `stock_industry_pe` - 行业市盈率数据
- `technical_indicator` - 技术指标数据
- `stock_industry_chain` - 产业链数据
- `stock_favorite` - 用户收藏股票

### 数据库配置

系统使用MySQL数据库，配置示例：

```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "stock_data",
        "USER": "your_username",
        "PASSWORD": "your_password",
        "HOST": "localhost",
        "PORT": "3306",
        "OPTIONS": {
            "charset": "utf8mb4",
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

## 部署说明

### 生产环境部署

1. **服务器要求**

   - Linux服务器（推荐Ubuntu 20.04+或CentOS 7+）
   - Python 3.12+
   - MySQL 8.0+
   - Nginx（用于静态文件服务）
   - 至少4GB内存，20GB存储空间
2. **部署步骤**

   ```bash
   # 1. 克隆代码
   git clone <repository_url>
   cd dj_stock

   # 2. 创建虚拟环境
   python3 -m venv venv
   source venv/bin/activate

   # 3. 安装依赖
   pip install -r requirements.txt

   # 4. 配置数据库
   # 编辑 dj_stock/settings.py 中的数据库配置

   # 5. 运行迁移
   python manage.py migrate

   # 6. 收集静态文件
   python manage.py collectstatic

   # 7. 创建超级用户
   python manage.py createsuperuser

   # 8. 使用Gunicorn启动
   gunicorn dj_stock.wsgi:application --bind 0.0.0.0:8000
   ```
3. **Nginx配置示例**

   ```nginx
   server {
       listen 80;
       server_name your_domain.com;

       location /static/ {
           alias /path/to/dj_stock/static/;
       }

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 维护和更新

### 数据更新策略

1. **实时数据**: 交易时间内每分钟更新行情数据
2. **日度数据**: 每日收盘后更新当日数据
3. **财务数据**: 每季度财报发布后更新
4. **基础数据**: 每周更新股票列表、板块成分股等

### 性能优化建议

1. **数据库优化**

   - 定期清理过期数据
   - 优化查询索引
   - 使用数据库连接池
2. **缓存策略**

   - 使用Redis缓存热点数据
   - 静态文件CDN加速
   - 数据库查询结果缓存
3. **监控和日志**

   - 配置应用性能监控
   - 设置错误日志告警
   - 定期备份数据库

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

### 开发规范

1. **代码风格**: 遵循PEP 8规范
2. **提交信息**: 使用清晰的提交信息
3. **测试**: 添加必要的单元测试
4. **文档**: 更新相关文档

### 问题反馈

如果遇到问题，请提供以下信息：

- 操作系统和Python版本
- 错误信息和堆栈跟踪
- 复现步骤
- 相关配置信息

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 文档地址: [Documentation]

---

**注意**: 本系统仅供学习和研究使用，不构成投资建议。使用本系统进行投资决策的风险由用户自行承担。
