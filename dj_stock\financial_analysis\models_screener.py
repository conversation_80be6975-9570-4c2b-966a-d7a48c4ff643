#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
选股筛选器相关模型

包括：
1. 用户自定义筛选策略
2. 筛选历史记录
3. 选股结果跟踪
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
import json


class StockScreenerStrategy(models.Model):
    """用户自定义筛选策略"""
    
    STRATEGY_TYPES = [
        ('value', '价值投资'),
        ('growth', '成长投资'),
        ('quality', '优质股'),
        ('dividend', '分红股'),
        ('technical', '技术分析'),
        ('custom', '自定义'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    name = models.CharField(max_length=100, verbose_name="策略名称")
    description = models.TextField(blank=True, verbose_name="策略描述")
    strategy_type = models.CharField(max_length=20, choices=STRATEGY_TYPES, default='custom', verbose_name="策略类型")
    
    # 筛选条件（JSON格式存储）
    filters = models.JSONField(default=dict, verbose_name="筛选条件")
    
    # 策略统计
    use_count = models.IntegerField(default=0, verbose_name="使用次数")
    last_used = models.DateTimeField(null=True, blank=True, verbose_name="最后使用时间")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    # 是否公开（其他用户可以看到和使用）
    is_public = models.BooleanField(default=False, verbose_name="是否公开")
    
    class Meta:
        db_table = "stock_screener_strategy"
        verbose_name = "选股策略"
        verbose_name_plural = verbose_name
        ordering = ['-updated_at']
        unique_together = [('user', 'name')]
    
    def __str__(self):
        return f"{self.user.username} - {self.name}"
    
    def get_filter_summary(self):
        """获取筛选条件摘要"""
        summary = []
        filters = self.filters
        
        if filters.get('industry'):
            summary.append(f"行业: {filters['industry']}")
        if filters.get('market_cap_min') or filters.get('market_cap_max'):
            min_cap = filters.get('market_cap_min', '不限')
            max_cap = filters.get('market_cap_max', '不限')
            summary.append(f"市值: {min_cap}-{max_cap}亿")
        if filters.get('roe_min'):
            summary.append(f"ROE≥{filters['roe_min']}%")
        if filters.get('pe_max'):
            summary.append(f"PE≤{filters['pe_max']}")
        
        return "; ".join(summary) if summary else "无特定条件"
    
    def increment_use_count(self):
        """增加使用次数"""
        self.use_count += 1
        self.last_used = timezone.now()
        self.save(update_fields=['use_count', 'last_used'])


class StockScreenerResult(models.Model):
    """筛选结果记录"""
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    strategy = models.ForeignKey(StockScreenerStrategy, on_delete=models.SET_NULL, 
                                null=True, blank=True, verbose_name="使用的策略")
    
    # 筛选条件快照
    filters_snapshot = models.JSONField(default=dict, verbose_name="筛选条件快照")
    
    # 筛选结果
    result_count = models.IntegerField(default=0, verbose_name="结果数量")
    stock_codes = models.JSONField(default=list, verbose_name="股票代码列表")
    
    # 筛选时的市场环境
    market_date = models.DateField(verbose_name="筛选日期")
    market_index = models.FloatField(null=True, blank=True, verbose_name="市场指数")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    
    class Meta:
        db_table = "stock_screener_result"
        verbose_name = "筛选结果"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.created_at.strftime('%Y-%m-%d')} - {self.result_count}只股票"


class StockScreenerPerformance(models.Model):
    """筛选结果表现跟踪"""
    
    screener_result = models.ForeignKey(StockScreenerResult, on_delete=models.CASCADE, verbose_name="筛选结果")
    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    
    # 筛选时的价格
    initial_price = models.FloatField(verbose_name="初始价格")
    initial_market_cap = models.FloatField(null=True, blank=True, verbose_name="初始市值")
    
    # 跟踪期间的表现
    performance_1d = models.FloatField(null=True, blank=True, verbose_name="1日涨跌幅")
    performance_1w = models.FloatField(null=True, blank=True, verbose_name="1周涨跌幅")
    performance_1m = models.FloatField(null=True, blank=True, verbose_name="1月涨跌幅")
    performance_3m = models.FloatField(null=True, blank=True, verbose_name="3月涨跌幅")
    performance_6m = models.FloatField(null=True, blank=True, verbose_name="6月涨跌幅")
    performance_1y = models.FloatField(null=True, blank=True, verbose_name="1年涨跌幅")
    
    # 最后更新时间
    last_updated = models.DateTimeField(auto_now=True, verbose_name="最后更新时间")
    
    class Meta:
        db_table = "stock_screener_performance"
        verbose_name = "筛选表现"
        verbose_name_plural = verbose_name
        unique_together = [('screener_result', 'stock_code')]
    
    def __str__(self):
        return f"{self.screener_result} - {self.stock_code}"


class StockWatchlist(models.Model):
    """股票观察列表"""
    
    WATCHLIST_TYPES = [
        ('screener', '筛选结果'),
        ('manual', '手动添加'),
        ('strategy', '策略跟踪'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    name = models.CharField(max_length=100, verbose_name="观察列表名称")
    description = models.TextField(blank=True, verbose_name="描述")
    watchlist_type = models.CharField(max_length=20, choices=WATCHLIST_TYPES, default='manual', verbose_name="类型")
    
    # 关联的筛选结果（如果是从筛选结果创建的）
    screener_result = models.ForeignKey(StockScreenerResult, on_delete=models.SET_NULL, 
                                       null=True, blank=True, verbose_name="筛选结果")
    
    # 股票列表
    stocks = models.ManyToManyField('StockWatchlistItem', blank=True, verbose_name="股票列表")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = "stock_watchlist"
        verbose_name = "观察列表"
        verbose_name_plural = verbose_name
        ordering = ['-updated_at']
        unique_together = [('user', 'name')]
    
    def __str__(self):
        return f"{self.user.username} - {self.name}"
    
    def get_stock_count(self):
        """获取股票数量"""
        return self.stocks.count()


class StockWatchlistItem(models.Model):
    """观察列表项目"""
    
    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    
    # 添加时的价格和市值
    add_price = models.FloatField(null=True, blank=True, verbose_name="添加时价格")
    add_market_cap = models.FloatField(null=True, blank=True, verbose_name="添加时市值")
    
    # 备注信息
    notes = models.TextField(blank=True, verbose_name="备注")
    
    # 时间戳
    added_at = models.DateTimeField(auto_now_add=True, verbose_name="添加时间")
    
    class Meta:
        db_table = "stock_watchlist_item"
        verbose_name = "观察列表项目"
        verbose_name_plural = verbose_name
        ordering = ['-added_at']
    
    def __str__(self):
        return f"{self.stock_name}({self.stock_code})"


class StockAlert(models.Model):
    """股票价格提醒"""
    
    ALERT_TYPES = [
        ('price_up', '价格上涨'),
        ('price_down', '价格下跌'),
        ('volume_up', '成交量放大'),
        ('technical', '技术指标'),
        ('news', '新闻事件'),
    ]
    
    ALERT_STATUS = [
        ('active', '激活'),
        ('triggered', '已触发'),
        ('disabled', '已禁用'),
    ]
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="用户")
    stock_code = models.CharField(max_length=10, verbose_name="股票代码")
    stock_name = models.CharField(max_length=50, verbose_name="股票名称")
    
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES, verbose_name="提醒类型")
    status = models.CharField(max_length=20, choices=ALERT_STATUS, default='active', verbose_name="状态")
    
    # 提醒条件
    target_price = models.FloatField(null=True, blank=True, verbose_name="目标价格")
    target_change_percent = models.FloatField(null=True, blank=True, verbose_name="目标涨跌幅")
    target_volume_ratio = models.FloatField(null=True, blank=True, verbose_name="目标量比")
    
    # 提醒设置
    message = models.TextField(verbose_name="提醒消息")
    is_email_enabled = models.BooleanField(default=False, verbose_name="邮件提醒")
    is_sms_enabled = models.BooleanField(default=False, verbose_name="短信提醒")
    
    # 触发信息
    triggered_at = models.DateTimeField(null=True, blank=True, verbose_name="触发时间")
    triggered_price = models.FloatField(null=True, blank=True, verbose_name="触发价格")
    
    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = "stock_alert"
        verbose_name = "股票提醒"
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.stock_name}({self.stock_code}) - {self.get_alert_type_display()}"
