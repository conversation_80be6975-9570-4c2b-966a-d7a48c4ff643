#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
高级股票筛选器视图

提供更全面的选股筛选功能，包括：
1. 基本面深度筛选
2. 技术指标筛选
3. 资金流向筛选
4. 行业概念筛选
5. 自定义筛选策略
"""

from django.views.generic import TemplateView
from django.db.models import Q, Avg, Count, Max, Min
from django.core.cache import cache
from django.core.paginator import Paginator
from django.db import connection
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
import time
import logging
from datetime import datetime, timedelta

from market_data.models import StockBasic, StockDailyQuote, StockFundFlow
from financial_analysis.models import StockFinancialIndicator, StockFavorite

logger = logging.getLogger(__name__)


@method_decorator(login_required, name='dispatch')
class AdvancedStockScreenerView(TemplateView):
    """高级股票筛选器"""
    template_name = "financial_analysis/advanced_screener.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取筛选参数
        filters = self.get_filter_params()
        
        # 执行筛选
        stocks = self.apply_filters(filters)
        
        # 分页处理
        paginator = Paginator(stocks, 50)
        page_number = self.request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)
        
        # 获取筛选选项数据
        context.update({
            'page_obj': page_obj,
            'stocks': page_obj,
            'filters': filters,
            'industries': self.get_industries(),
            'concepts': self.get_concepts(),
            'total_count': len(stocks),
        })
        
        return context

    def get_filter_params(self):
        """获取所有筛选参数"""
        return {
            # 基本筛选
            'industry': self.request.GET.get('industry', ''),
            'concept': self.request.GET.get('concept', ''),
            'market_cap_min': self.request.GET.get('market_cap_min', ''),
            'market_cap_max': self.request.GET.get('market_cap_max', ''),
            
            # 估值指标
            'pe_min': self.request.GET.get('pe_min', ''),
            'pe_max': self.request.GET.get('pe_max', ''),
            'pb_min': self.request.GET.get('pb_min', ''),
            'pb_max': self.request.GET.get('pb_max', ''),
            'peg_min': self.request.GET.get('peg_min', ''),
            'peg_max': self.request.GET.get('peg_max', ''),
            
            # 盈利能力
            'roe_min': self.request.GET.get('roe_min', ''),
            'roe_max': self.request.GET.get('roe_max', ''),
            'roa_min': self.request.GET.get('roa_min', ''),
            'roa_max': self.request.GET.get('roa_max', ''),
            'net_profit_margin_min': self.request.GET.get('net_profit_margin_min', ''),
            'net_profit_margin_max': self.request.GET.get('net_profit_margin_max', ''),
            'gross_profit_margin_min': self.request.GET.get('gross_profit_margin_min', ''),
            'gross_profit_margin_max': self.request.GET.get('gross_profit_margin_max', ''),
            
            # 成长性指标
            'revenue_growth_min': self.request.GET.get('revenue_growth_min', ''),
            'revenue_growth_max': self.request.GET.get('revenue_growth_max', ''),
            'profit_growth_min': self.request.GET.get('profit_growth_min', ''),
            'profit_growth_max': self.request.GET.get('profit_growth_max', ''),
            'revenue_cagr_3y_min': self.request.GET.get('revenue_cagr_3y_min', ''),
            'revenue_cagr_3y_max': self.request.GET.get('revenue_cagr_3y_max', ''),
            
            # 财务健康度
            'debt_ratio_min': self.request.GET.get('debt_ratio_min', ''),
            'debt_ratio_max': self.request.GET.get('debt_ratio_max', ''),
            'current_ratio_min': self.request.GET.get('current_ratio_min', ''),
            'current_ratio_max': self.request.GET.get('current_ratio_max', ''),
            'cash_ratio_min': self.request.GET.get('cash_ratio_min', ''),
            'cash_ratio_max': self.request.GET.get('cash_ratio_max', ''),
            
            # 现金流指标
            'ocf_to_profit_min': self.request.GET.get('ocf_to_profit_min', ''),
            'ocf_to_profit_max': self.request.GET.get('ocf_to_profit_max', ''),
            'free_cash_flow_min': self.request.GET.get('free_cash_flow_min', ''),
            'free_cash_flow_max': self.request.GET.get('free_cash_flow_max', ''),
            
            # 分红指标
            'dividend_yield_min': self.request.GET.get('dividend_yield_min', ''),
            'dividend_yield_max': self.request.GET.get('dividend_yield_max', ''),
            'dividend_years_min': self.request.GET.get('dividend_years_min', ''),
            
            # 技术指标
            'price_position_min': self.request.GET.get('price_position_min', ''),  # 相对52周高点位置
            'price_position_max': self.request.GET.get('price_position_max', ''),
            'volume_ratio_min': self.request.GET.get('volume_ratio_min', ''),  # 量比
            'volume_ratio_max': self.request.GET.get('volume_ratio_max', ''),
            'ma_arrangement': self.request.GET.get('ma_arrangement', ''),  # 均线排列
            
            # 资金流向
            'fund_flow_days': self.request.GET.get('fund_flow_days', '5'),  # 资金流向天数
            'main_fund_flow_min': self.request.GET.get('main_fund_flow_min', ''),
            'main_fund_flow_max': self.request.GET.get('main_fund_flow_max', ''),
            
            # 特殊筛选
            'continuous_roe_years': self.request.GET.get('continuous_roe_years', ''),
            'continuous_growth_years': self.request.GET.get('continuous_growth_years', ''),
            'new_high_days': self.request.GET.get('new_high_days', ''),  # 创新高天数
            
            # 排序
            'sort_by': self.request.GET.get('sort_by', 'market_cap'),
            'sort_order': self.request.GET.get('sort_order', 'desc'),
        }

    def apply_filters(self, filters):
        """应用筛选条件"""
        # 获取基础股票数据
        stocks = self.get_base_stocks(filters)
        
        # 应用财务指标筛选
        stocks = self.apply_financial_filters(stocks, filters)
        
        # 应用技术指标筛选
        stocks = self.apply_technical_filters(stocks, filters)
        
        # 应用资金流向筛选
        stocks = self.apply_fund_flow_filters(stocks, filters)
        
        # 应用特殊筛选条件
        stocks = self.apply_special_filters(stocks, filters)
        
        # 排序
        stocks = self.sort_stocks(stocks, filters)
        
        return stocks

    def get_base_stocks(self, filters):
        """获取基础股票数据"""
        query = Q()
        
        # 行业筛选
        if filters['industry']:
            query &= Q(industry=filters['industry'])
            
        # 概念筛选（需要关联概念板块表）
        if filters['concept']:
            # 这里需要根据实际的概念板块关联表来实现
            pass
            
        stocks = StockBasic.objects.filter(query)
        return list(stocks)

    def get_industries(self):
        """获取所有行业"""
        return StockBasic.objects.values_list('industry', flat=True).distinct().order_by('industry')

    def get_concepts(self):
        """获取所有概念（需要根据实际数据结构实现）"""
        # 这里需要根据实际的概念板块数据来实现
        return []

    def apply_financial_filters(self, stocks, filters):
        """应用财务指标筛选"""
        # 这里需要实现具体的财务指标筛选逻辑
        # 由于代码较长，这里只提供框架
        return stocks

    def apply_technical_filters(self, stocks, filters):
        """应用技术指标筛选"""
        # 实现技术指标筛选逻辑
        return stocks

    def apply_fund_flow_filters(self, stocks, filters):
        """应用资金流向筛选"""
        # 实现资金流向筛选逻辑
        return stocks

    def apply_special_filters(self, stocks, filters):
        """应用特殊筛选条件"""
        # 实现特殊筛选逻辑，如连续ROE、连续增长等
        return stocks

    def sort_stocks(self, stocks, filters):
        """对股票进行排序"""
        sort_by = filters['sort_by']
        sort_order = filters['sort_order']
        
        # 实现排序逻辑
        return stocks


class StockScreenerPresetView(TemplateView):
    """预设筛选策略"""
    template_name = "financial_analysis/screener_presets.html"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 定义预设筛选策略
        presets = {
            'value_stocks': {
                'name': '价值股筛选',
                'description': '低估值、高分红、财务稳健的价值股',
                'filters': {
                    'pe_max': 15,
                    'pb_max': 2,
                    'roe_min': 10,
                    'debt_ratio_max': 50,
                    'dividend_yield_min': 3,
                }
            },
            'growth_stocks': {
                'name': '成长股筛选',
                'description': '高成长、高ROE的成长股',
                'filters': {
                    'revenue_growth_min': 20,
                    'profit_growth_min': 25,
                    'roe_min': 15,
                    'revenue_cagr_3y_min': 15,
                }
            },
            'quality_stocks': {
                'name': '优质股筛选',
                'description': '连续盈利、ROE稳定的优质股',
                'filters': {
                    'continuous_roe_years': 3,
                    'roe_min': 15,
                    'debt_ratio_max': 40,
                    'current_ratio_min': 1.5,
                }
            },
            'dividend_stocks': {
                'name': '分红股筛选',
                'description': '高股息率、连续分红的股票',
                'filters': {
                    'dividend_yield_min': 4,
                    'dividend_years_min': 5,
                    'roe_min': 8,
                }
            },
        }
        
        context['presets'] = presets
        return context
