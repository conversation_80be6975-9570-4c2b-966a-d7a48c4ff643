<!-- 通用金融走势图表组件 -->
<div class="card">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h3 class="card-title">{{ chart_title|default:"走势图" }}</h3>
      <div class="d-flex gap-2 align-items-center">
        <!-- 数据范围选择 -->
        <select class="form-select form-select-sm" id="data-range-select" style="width: auto">
          <option value="30">近30天</option>
          <option value="60">近60天</option>
          <option value="90">近90天</option>
          <option value="250">近1年</option>
          <option value="500" selected>近2年</option>
          <option value="750">近3年</option>
          <option value="1000">近4年</option>
          <option value="0">全部数据</option>
        </select>

        <!-- 时间周期切换 -->
        <div class="btn-group" role="group" aria-label="时间周期">
          <button type="button" class="btn btn-outline-primary btn-sm period-btn active" data-period="daily">日线</button>
          <button type="button" class="btn btn-outline-primary btn-sm period-btn" data-period="weekly">周线</button>
          <button type="button" class="btn btn-outline-primary btn-sm period-btn" data-period="monthly">月线</button>
          <button type="button" class="btn btn-outline-primary btn-sm period-btn" data-period="yearly">年线</button>
        </div>
      </div>
    </div>
  </div>
  <div class="card-body">
    <!-- 移动平均线选择和实时价格显示 -->
    <div class="mb-3 d-flex justify-content-between align-items-center">
      <div>
        <div class="form-check form-check-inline">
          <input class="form-check-input ma-checkbox" type="checkbox" id="ma5" value="5" checked />
          <label class="form-check-label" for="ma5">5<span class="period-text">日</span>线 <span class="ma-value text-muted" id="ma5-value">-</span></label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input ma-checkbox" type="checkbox" id="ma30" value="30" checked />
          <label class="form-check-label" for="ma30">30<span class="period-text">日</span>线 <span class="ma-value text-muted" id="ma30-value">-</span></label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input ma-checkbox" type="checkbox" id="ma60" value="60" />
          <label class="form-check-label" for="ma60">60<span class="period-text">日</span>线 <span class="ma-value text-muted" id="ma60-value">-</span></label>
        </div>
        <div class="form-check form-check-inline">
          <input class="form-check-input ma-checkbox" type="checkbox" id="ma90" value="90" />
          <label class="form-check-label" for="ma90">90<span class="period-text">日</span>线 <span class="ma-value text-muted" id="ma90-value">-</span></label>
        </div>
      </div>

      <!-- 实时价格信息 -->
      <div class="price-info">
        <div class="d-flex align-items-center">
          <span class="fw-bold me-2">最新价:</span>
          <span id="latest-price" class="fw-bold fs-4">-</span>
          <span id="price-change" class="ms-2 badge">-</span>
        </div>
      </div>
    </div>

    <!-- 图表容器 -->
    <div id="universal-chart" style="height: 500px"></div>
  </div>
</div>

<script>
  // 通用金融图表组件
  class UniversalFinancialChart {
    constructor(containerId, data, options = {}) {
      this.containerId = containerId
      this.originalData = data
      this.currentData = data
      this.currentPeriod = 'daily'
      this.chart = null
      this.options = {
        code: options.code || '',
        name: options.name || '',
        type: options.type || 'stock', // 'stock' 或 'index'
        apiUrl: options.apiUrl || '',
        ...options,
      }

      this.init()
    }

    init() {
      this.chart = echarts.init(document.getElementById(this.containerId))
      this.bindEvents()
      this.updateChart()
    }

    bindEvents() {
      // 时间周期切换
      document.querySelectorAll('.period-btn').forEach((btn) => {
        btn.addEventListener('click', (e) => {
          document.querySelectorAll('.period-btn').forEach((b) => b.classList.remove('active'))
          e.target.classList.add('active')
          this.currentPeriod = e.target.dataset.period
          this.updatePeriodText()
          this.processData()
          this.updateChart()
        })
      })

      // 移动平均线切换
      document.querySelectorAll('.ma-checkbox').forEach((checkbox) => {
        checkbox.addEventListener('change', () => {
          this.updateChart()
        })
      })

      // 数据范围选择
      const rangeSelect = document.getElementById('data-range-select')
      if (rangeSelect) {
        rangeSelect.addEventListener('change', (e) => {
          const days = parseInt(e.target.value)
          this.loadData(days)
        })
      }
    }

    async loadData(days = 500) {
      try {
        const url = `${this.options.apiUrl}?days=${days}`
        const response = await fetch(url)
        const result = await response.json()

        if (result.success) {
          this.originalData = result.data
          this.processData()
          this.updateChart()
        } else {
          console.error('加载数据失败:', result.error)
        }
      } catch (error) {
        console.error('网络请求失败:', error)
      }
    }

    updatePeriodText() {
      const periodMap = {
        daily: '日',
        weekly: '周',
        monthly: '月',
        yearly: '年',
      }

      document.querySelectorAll('.period-text').forEach((span) => {
        span.textContent = periodMap[this.currentPeriod]
      })
    }

    processData() {
      if (this.currentPeriod === 'daily') {
        this.currentData = this.originalData
        return
      }

      // 根据周期聚合数据
      const aggregatedData = []
      let currentPeriodData = []
      let lastPeriodKey = null

      this.originalData.forEach((item) => {
        const date = new Date(item.date)
        let periodKey

        switch (this.currentPeriod) {
          case 'weekly':
            const weekStart = new Date(date)
            weekStart.setDate(date.getDate() - date.getDay() + 1)
            periodKey = weekStart.toISOString().split('T')[0]
            break
          case 'monthly':
            periodKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
            break
          case 'yearly':
            periodKey = date.getFullYear().toString()
            break
        }

        if (lastPeriodKey !== periodKey) {
          if (currentPeriodData.length > 0) {
            aggregatedData.push(this.aggregatePeriodData(currentPeriodData))
          }
          currentPeriodData = [item]
          lastPeriodKey = periodKey
        } else {
          currentPeriodData.push(item)
        }
      })

      if (currentPeriodData.length > 0) {
        aggregatedData.push(this.aggregatePeriodData(currentPeriodData))
      }

      this.currentData = aggregatedData
    }

    aggregatePeriodData(periodData) {
      if (periodData.length === 0) return null

      const first = periodData[0]
      const last = periodData[periodData.length - 1]

      return {
        date: last.date,
        open: first.open,
        close: last.close,
        high: Math.max(...periodData.map((d) => d.high)),
        low: Math.min(...periodData.map((d) => d.low)),
        volume: periodData.reduce((sum, d) => sum + d.volume, 0),
        amount: periodData.reduce((sum, d) => sum + d.amount, 0),
      }
    }

    calculateMA(data, period) {
      const result = []
      for (let i = 0; i < data.length; i++) {
        if (i < period - 1) {
          result.push(null)
        } else {
          const sum = data.slice(i - period + 1, i + 1).reduce((acc, item) => acc + item.close, 0)
          result.push((sum / period).toFixed(2))
        }
      }
      return result
    }

    updateChart() {
      const dates = this.currentData.map((item) => item.date)
      const klineData = this.currentData.map((item) => [item.open, item.close, item.low, item.high])
      const volumes = this.currentData.map((item) => item.volume)
      const amounts = this.currentData.map((item) => item.amount)

      // 获取选中的移动平均线
      const selectedMAs = []
      document.querySelectorAll('.ma-checkbox:checked').forEach((checkbox) => {
        const period = parseInt(checkbox.value)
        const maData = this.calculateMA(this.currentData, period)
        selectedMAs.push({
          period: period,
          data: maData,
        })

        // 更新移动平均线的最新值显示
        const latestValue = maData[maData.length - 1]
        const valueElement = document.getElementById(`ma${period}-value`)
        if (valueElement && latestValue !== null) {
          valueElement.textContent = `(${latestValue})`
          valueElement.classList.remove('text-muted')
          valueElement.classList.add('text-primary')
        }
      })

      // 更新最新价格显示
      if (this.currentData.length > 0) {
        const latestData = this.currentData[this.currentData.length - 1]
        const latestPriceElement = document.getElementById('latest-price')
        const priceChangeElement = document.getElementById('price-change')

        if (latestPriceElement) {
          latestPriceElement.textContent = latestData.close.toFixed(2)
        }

        if (priceChangeElement && this.currentData.length > 1) {
          const prevData = this.currentData[this.currentData.length - 2]
          const change = latestData.close - prevData.close
          const changePercent = (change / prevData.close) * 100

          priceChangeElement.textContent = `${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent.toFixed(2)}%)`
          priceChangeElement.className = `ms-2 badge ${change >= 0 ? 'bg-danger' : 'bg-success'} text-white`
        }
      }

      const series = [
        {
          name: this.options.name,
          type: 'candlestick',
          data: klineData,
          itemStyle: {
            color: '#ef4444',
            color0: '#22c55e',
            borderColor: '#ef4444',
            borderColor0: '#22c55e',
          },
        },
      ]

      // 添加移动平均线
      selectedMAs.forEach((ma) => {
        series.push({
          name: `MA${ma.period}`,
          type: 'line',
          data: ma.data,
          smooth: true,
          lineStyle: {
            width: 2,
          },
          showSymbol: false,
        })
      })

      const option = {
        title: {
          text: `${this.options.name}(${this.options.code})`,
          left: 'center',
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter: function (params) {
            const dataIndex = params[0].dataIndex
            let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`

            const currentVolume = volumes[dataIndex]
            const currentAmount = amounts[dataIndex]

            params.forEach((param) => {
              if (param.seriesName === this.options.name) {
                const data = param.data
                const prevClose = dataIndex > 0 ? klineData[dataIndex - 1][1] : data[1]
                const change = data[1] - prevClose
                const changePercent = ((change / prevClose) * 100).toFixed(2)
                const changeColor = change >= 0 ? '#ef4444' : '#22c55e'

                result += `<div style="border-bottom: 1px solid #eee; padding-bottom: 6px; margin-bottom: 6px;">`
                result += `<div>开盘: <span style="color: #666;">${data[0].toFixed(2)}</span></div>`
                result += `<div>收盘: <span style="color: #666;">${data[1].toFixed(2)}</span></div>`
                result += `<div>最高: <span style="color: #ef4444;">${data[3].toFixed(2)}</span></div>`
                result += `<div>最低: <span style="color: #22c55e;">${data[2].toFixed(2)}</span></div>`
                result += `<div>涨跌: <span style="color: ${changeColor};">${change >= 0 ? '+' : ''}${change.toFixed(2)} (${changePercent}%)</span></div>`
                result += `</div>`
              }
            })

            // 添加成交量和成交额信息
            result += `<div>`
            if (this.options.type === 'stock') {
              result += `<div>成交量: <span style="color: #666;">${currentVolume.toFixed(2)}万手</span></div>`
              result += `<div>成交额: <span style="color: #666;">${currentAmount.toFixed(2)}亿元</span></div>`
            } else if (this.options.type === 'board') {
              result += `<div>换手率: <span style="color: #666;">${currentVolume.toFixed(2)}%</span></div>`
              // 优化总市值显示单位
              const marketValueText = currentAmount >= 10000 ? `${(currentAmount / 10000).toFixed(1)}万亿元` : `${currentAmount.toFixed(0)}亿元`
              result += `<div>总市值: <span style="color: #666;">${marketValueText}</span></div>`
            } else {
              result += `<div>成交量: <span style="color: #666;">${currentVolume.toFixed(2)}亿手</span></div>`
              result += `<div>成交额: <span style="color: #666;">${currentAmount.toFixed(2)}亿元</span></div>`
            }
            result += `</div>`

            // 添加移动平均线信息
            const maInfo = []
            params.forEach((param) => {
              if (param.seriesName.startsWith('MA') && param.data !== null) {
                maInfo.push(`<span style="color: ${param.color};">${param.seriesName}: ${param.data}</span>`)
              }
            })

            if (maInfo.length > 0) {
              result += `<div style="border-top: 1px solid #eee; padding-top: 6px; margin-top: 6px;">`
              result += maInfo.join('<br/>')
              result += `</div>`
            }

            return result
          }.bind(this),
        },
        legend: {
          data: this.options.type === 'board' ? [...series.map((s) => s.name), '换手率', '总市值'] : [...series.map((s) => s.name), '成交量', '成交额'],
          top: 30,
        },
        grid: [
          {
            left: '8%',
            right: '8%',
            top: '15%',
            height: '58%',
          },
          {
            left: '8%',
            right: '8%',
            top: '78%',
            height: '15%',
          },
        ],
        xAxis: [
          {
            type: 'category',
            data: dates,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            splitLine: { show: false },
            splitNumber: 20,
            min: 'dataMin',
            max: 'dataMax',
          },
          {
            type: 'category',
            gridIndex: 1,
            data: dates,
            scale: true,
            boundaryGap: false,
            axisLine: { onZero: false },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: { show: false },
            splitNumber: 20,
            min: 'dataMin',
            max: 'dataMax',
          },
        ],
        yAxis: [
          {
            scale: true,
            splitArea: {
              show: true,
            },
          },
          {
            scale: true,
            gridIndex: 1,
            splitNumber: 2,
            axisLabel: {
              show: true,
              formatter: function (value) {
                if (this.options.type === 'stock') {
                  return value.toFixed(1) + ' 万手'
                } else if (this.options.type === 'board') {
                  return value.toFixed(1) + ' %'
                } else {
                  return value.toFixed(1) + ' 亿手'
                }
              }.bind(this),
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
          },
          {
            scale: true,
            gridIndex: 1,
            splitNumber: 2,
            position: 'right',
            axisLabel: {
              show: true,
              formatter: function (value) {
                if (this.options.type === 'board') {
                  // 板块总市值已经转换为亿元
                  return value >= 10000 ? (value / 10000).toFixed(1) + ' 万亿' : value.toFixed(0) + ' 亿'
                } else {
                  return value.toFixed(1) + ' 亿元'
                }
              }.bind(this),
            },
            axisLine: { show: false },
            axisTick: { show: false },
            splitLine: { show: false },
          },
        ],
        dataZoom: [
          {
            type: 'inside',
            xAxisIndex: [0, 1],
            start: 80,
            end: 100,
          },
          {
            show: true,
            xAxisIndex: [0, 1],
            type: 'slider',
            top: '94%',
            start: 80,
            end: 100,
          },
        ],
        series: [
          ...series,
          {
            name: this.options.type === 'board' ? '换手率' : '成交量',
            type: 'bar',
            xAxisIndex: 1,
            yAxisIndex: 1,
            data: volumes,
            itemStyle: {
              color: function (params) {
                const dataIndex = params.dataIndex
                if (dataIndex === 0) return '#999'
                const current = klineData[dataIndex]
                const prev = klineData[dataIndex - 1]
                return current[1] >= prev[1] ? '#ef4444' : '#22c55e'
              },
            },
            barWidth: '60%',
          },
          {
            name: this.options.type === 'board' ? '总市值' : '成交额',
            type: 'line',
            xAxisIndex: 1,
            yAxisIndex: 2,
            data: amounts,
            lineStyle: {
              color: '#ffa500',
              width: 2,
            },
            symbol: 'none',
            smooth: true,
          },
        ],
      }

      this.chart.setOption(option, true)
    }
  }

  // 全局函数，供外部调用
  window.initUniversalChart = function (containerId, data, options) {
    return new UniversalFinancialChart(containerId, data, options)
  }
</script>
