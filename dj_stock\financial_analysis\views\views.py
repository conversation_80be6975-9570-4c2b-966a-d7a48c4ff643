import json
import logging

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.db.models import Q
from django.http import JsonResponse
from django.views.decorators.http import require_GET, require_POST
from django.views.decorators.csrf import csrf_exempt
from django.contrib import messages
from ..models import (
    StockDividend,
    StockFinancialIndicator,
    StockIndustryChain,
    StockShareholderStatistics,
    TechnicalIndicator,
    StockShareholderDetail,
    StockFavorite,
)

logger = logging.getLogger(__name__)
from datetime import datetime, timedelta
from ..utils.financial_fetcher import FinancialDataFetcher
import logging
from market_data.models import StockBasic, StockDailyQuote
from financial_analysis.models import StockFinancialIndicator
from django.db.models.functions import TruncDate
from django.db.models import Count, Sum, Avg
import json
from decimal import Decimal
from django.views.generic import TemplateView

logger = logging.getLogger(__name__)


def dividend_list(request):
    """
    分红派息列表视图
    """
    # 获取查询参数
    query = request.GET.get("q", "")
    year = request.GET.get("year", "")
    min_cash_dividend = request.GET.get("min_cash_dividend", "")
    max_cash_dividend = request.GET.get("max_cash_dividend", "")
    min_share_dividend = request.GET.get("min_share_dividend", "")
    max_share_dividend = request.GET.get("max_share_dividend", "")
    min_share_transfer = request.GET.get("min_share_transfer", "")
    max_share_transfer = request.GET.get("max_share_transfer", "")
    implementation_status = request.GET.get("implementation_status", "")
    sort = request.GET.get("sort", "-registration_date")  # 默认排序
    secondary_sort = request.GET.get("secondary_sort", "")
    rows_per_page = int(request.GET.get("rows_per_page", 20))

    # 查询数据
    dividends = StockDividend.objects.all()

    # 应用过滤条件
    if query:
        dividends = dividends.filter(
            Q(stock_code__icontains=query) | Q(stock_name__icontains=query)
        )
    if year:
        dividends = dividends.filter(
            Q(announce_date__year=year) | Q(registration_date__year=year)
        )

    # 现金分红筛选
    if min_cash_dividend:
        try:
            dividends = dividends.filter(cash_dividend__gte=float(min_cash_dividend))
        except ValueError:
            pass
    if max_cash_dividend:
        try:
            dividends = dividends.filter(cash_dividend__lte=float(max_cash_dividend))
        except ValueError:
            pass

    # 送股筛选
    if min_share_dividend:
        try:
            dividends = dividends.filter(share_dividend__gte=float(min_share_dividend))
        except ValueError:
            pass
    if max_share_dividend:
        try:
            dividends = dividends.filter(share_dividend__lte=float(max_share_dividend))
        except ValueError:
            pass

    # 转增筛选
    if min_share_transfer:
        try:
            dividends = dividends.filter(share_transfer__gte=float(min_share_transfer))
        except ValueError:
            pass
    if max_share_transfer:
        try:
            dividends = dividends.filter(share_transfer__lte=float(max_share_transfer))
        except ValueError:
            pass

    # 实施状态筛选
    if implementation_status:
        dividends = dividends.filter(implementation_status=implementation_status)

    # 应用排序
    if sort:
        dividends = dividends.order_by(sort)
        if secondary_sort:  # 应用次级排序
            dividends = dividends.order_by(sort, secondary_sort)
    else:
        dividends = dividends.order_by("-registration_date")

    # 记录总数
    total_dividends = dividends.count()

    # 分页
    paginator = Paginator(dividends, rows_per_page)
    page_number = request.GET.get("page", 1)
    try:
        page_obj = paginator.page(page_number)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    # 获取所有年份列表
    all_years = StockDividend.objects.dates("registration_date", "year", order="DESC")
    all_years = [date.year for date in all_years]

    # 获取所有实施状态
    implementation_statuses = StockDividend.objects.values_list(
        "implementation_status", flat=True
    ).distinct()

    context = {
        "page_obj": page_obj,
        "query": query,
        "year": year,
        "min_cash_dividend": min_cash_dividend,
        "max_cash_dividend": max_cash_dividend,
        "min_share_dividend": min_share_dividend,
        "max_share_dividend": max_share_dividend,
        "min_share_transfer": min_share_transfer,
        "max_share_transfer": max_share_transfer,
        "implementation_status": implementation_status,
        "implementation_statuses": implementation_statuses,
        "sort": sort,
        "secondary_sort": secondary_sort,
        "all_years": all_years,
        "total_dividends": total_dividends,
        "rows_per_page": rows_per_page,
    }

    return render(request, "financial_analysis/dividend.html", context)


def financial_report_list(request):
    """财务报告列表视图"""
    # 获取查询参数
    stock_code = request.GET.get("stock_code", "")
    stock_name = request.GET.get("stock_name", "")
    year = request.GET.get("year", "")

    # 构建查询条件
    query = Q()
    if stock_code:
        query &= Q(stock_code__icontains=stock_code)
    if year:
        query &= Q(report_date__year=year)

    # 获取所有年份列表
    all_years = StockFinancialIndicator.objects.dates(
        "report_date", "year", order="DESC"
    )
    all_years = [date.year for date in all_years]

    # 获取财务报告数据
    financials = StockFinancialIndicator.objects.filter(query).order_by("-report_date")

    # 获取股票名称
    stock_codes = set(financials.values_list("stock_code", flat=True))
    stock_names = {
        stock.stock_code: stock.stock_name
        for stock in StockBasic.objects.filter(stock_code__in=stock_codes)
    }

    # 处理单位转换（从元转换为亿元）并添加股票名称
    for financial in financials:
        if financial.total_revenue:
            financial.total_revenue = (
                float(financial.total_revenue) / 100000000
            )  # 转换为亿元
        financial.stock_name = stock_names.get(financial.stock_code, "")

    # 如果指定了股票名称过滤
    if stock_name:
        financials = [
            f for f in financials if stock_name.lower() in f.stock_name.lower()
        ]

    # 分页处理
    paginator = Paginator(financials, 20)
    page = request.GET.get("page", 1)
    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        "financials": page_obj,
        "page_obj": page_obj,
        "year": year,
        "all_years": all_years,
    }

    return render(request, "financial_analysis/financial_report_list.html", context)


def financial_indicator(request, code):
    """财务指标详情视图"""
    # 初始化上下文
    context = {
        "stock_code": code,
        "data_status": "info",
        "data_status_message": None,
    }

    try:
        # 检查是否已有该股票的数据
        existing_data = StockFinancialIndicator.objects.filter(stock_code=code)
        should_fetch = not existing_data.exists()  # 如果没有数据，需要采集

        if not should_fetch:
            # 检查是否有最新数据（假设最新数据不超过90天）
            latest_data = existing_data.order_by("-report_date").first()
            if latest_data:
                latest_date = latest_data.report_date
                current_date = datetime.now().date()
                days_diff = (current_date - latest_date).days
                should_fetch = days_diff > 90  # 如果最新数据超过90天，需要更新

                if should_fetch:
                    context["data_status"] = "warning"
                    context["data_status_message"] = (
                        f"数据已过期（{days_diff}天），正在更新..."
                    )
                else:
                    context["data_status"] = "success"
                    context["data_status_message"] = (
                        f"数据已是最新（{days_diff}天前更新）"
                    )
        else:
            context["data_status"] = "info"
            context["data_status_message"] = "首次采集数据，请稍候..."

        # 如果需要采集数据
        if should_fetch:
            logger.info(f"开始采集股票 {code} 的财务指标数据")
            fetcher = FinancialDataFetcher()
            try:
                success, message = fetcher.fetch_and_save(code)
                if not success:
                    logger.error(f"获取股票 {code} 的财务数据失败: {message}")
                    context["data_status"] = "error"
                    context["data_status_message"] = f"数据采集失败：{message}"
                else:
                    context["data_status"] = "complete"
                    context["data_status_message"] = "数据采集完成"
            except Exception as e:
                error_msg = str(e)
                logger.error(f"处理股票 {code} 的财务数据时发生错误: {error_msg}")
                context["data_status"] = "error"
                context["data_status_message"] = f"处理数据时发生错误：{error_msg}"

        # 获取财务指标数据
        indicators = StockFinancialIndicator.objects.filter(stock_code=code).order_by(
            "-report_date"
        )

        # 从StockBasic模型获取股票名称
        stock = StockBasic.objects.filter(stock_code=code).first()
        stock_name = stock.stock_name if stock else ""

        # 准备图表数据
        indicators_data = []
        for indicator in indicators:
            # 转换为亿元单位
            total_revenue = float(indicator.total_revenue or 0) / 100000000
            # 使用net_assets替代total_assets
            net_assets = float(indicator.net_assets or 0) / 100000000
            # 使用deducted_net_profit替代operating_cash_flow
            deducted_net_profit = float(indicator.deducted_net_profit or 0) / 100000000

            indicators_data.append(
                {
                    "report_date": indicator.report_date.strftime("%Y-%m-%d"),
                    "total_revenue": total_revenue,
                    "total_revenue_growth": float(indicator.total_revenue_growth or 0),
                    "net_assets": net_assets,
                    "deducted_net_profit": deducted_net_profit,
                    "eps": float(indicator.eps or 0),
                    "nav": float(indicator.nav or 0),
                    "capital_reserve": float(indicator.capital_reserve or 0),
                    "undistributed_profit": float(indicator.undistributed_profit or 0),
                    "ocf_per_share": float(indicator.ocf_per_share or 0),
                    "net_profit_margin": float(indicator.net_profit_margin or 0),
                    "gross_profit_margin": float(indicator.gross_profit_margin or 0),
                    "roe": float(indicator.roe or 0),
                    "diluted_roe": float(indicator.diluted_roe or 0),
                    # 计算营业周期 = 存货周转天数 + 应收账款周转天数
                    "operating_cycle": float(
                        (indicator.inventory_days or 0)
                        + (indicator.receivable_days or 0)
                    ),
                    "inventory_turnover": float(indicator.inventory_turnover or 0),
                    "inventory_days": float(indicator.inventory_days or 0),
                    "receivable_days": float(indicator.receivable_days or 0),
                    "current_ratio": float(indicator.current_ratio or 0),
                    "quick_ratio": float(indicator.quick_ratio or 0),
                    "conservative_quick_ratio": float(
                        indicator.conservative_quick_ratio or 0
                    ),
                    "equity_ratio": float(indicator.equity_ratio or 0),
                    "debt_asset_ratio": float(indicator.debt_asset_ratio or 0),
                }
            )

        context.update(
            {
                "indicators": indicators_data,
                "stock_name": stock_name,
                "indicators_json": json.dumps(indicators_data),
            }
        )

    except Exception as e:
        error_msg = str(e)
        logger.error(f"处理股票 {code} 的财务指标页面时发生错误: {error_msg}")
        context.update(
            {
                "data_status": "error",
                "data_status_message": f"页面加载错误：{error_msg}",
                "indicators": [],
                "stock_name": "",
                "indicators_json": "[]",
            }
        )

    return render(request, "financial_analysis/financial_indicator.html", context)


def industry_chain(request):
    """产业链视图"""
    search_query = request.GET.get("search", "")

    # 构建查询
    chains = StockIndustryChain.objects.all()

    # 搜索过滤
    if search_query:
        chains = chains.filter(
            Q(stock_code__icontains=search_query)
            | Q(stock_name__icontains=search_query)
            | Q(industry_chain__icontains=search_query)
        )

    # 排序
    chains = chains.order_by("industry_chain", "position")

    # 分页
    paginator = Paginator(chains, 10)  # 每页10条记录
    page = request.GET.get("page", 1)

    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        "page_obj": page_obj,
        "search_query": search_query,
    }

    return render(request, "financial_analysis/industry_chain.html", context)


def shareholder_statistics(request, code):
    """股东统计视图"""
    # 获取查询参数
    shareholder_type = request.GET.get("shareholder_type", "")
    search_query = request.GET.get("search", "")

    # 获取股票名称
    stock = get_object_or_404(StockBasic, stock_code=code)
    stock_name = stock.stock_name

    # 查询数据 - 注意：StockShareholderStatistics 模型没有 stock_code 字段
    # 我们需要通过股东持股明细来获取与该股票相关的股东
    # 首先获取该股票的所有股东名称
    shareholder_names = (
        StockShareholderDetail.objects.filter(stock_code=code)
        .values_list("shareholder_name", flat=True)
        .distinct()
    )

    # 然后查询这些股东的统计数据
    statistics = StockShareholderStatistics.objects.filter(
        shareholder_name__in=shareholder_names
    )

    # 应用过滤条件
    if shareholder_type:
        statistics = statistics.filter(shareholder_type=shareholder_type)

    # 应用搜索过滤
    if search_query:
        statistics = statistics.filter(shareholder_name__icontains=search_query)

    # 按报告日期降序排序
    statistics = statistics.order_by("-report_date")

    # 分页处理
    paginator = Paginator(statistics, 20)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 准备图表数据
    statistics_data = []
    for stat in statistics:
        statistics_data.append(
            {
                "report_date": stat.report_date.strftime("%Y-%m-%d"),
                "count": int(stat.count or 0),
                "avg_return_10d": float(stat.avg_return_10d or 0),
                "avg_return_30d": float(stat.avg_return_30d or 0),
                "avg_return_60d": float(stat.avg_return_60d or 0),
            }
        )

    context = {
        "statistics": statistics,
        "page_obj": page_obj,
        "stock_code": code,
        "stock_name": stock_name,
        "shareholder_type": shareholder_type,
        "search_query": search_query,
        "statistics_json": json.dumps(statistics_data),
        "total_items": statistics.count(),
    }

    return render(request, "financial_analysis/shareholder_statistics.html", context)


# API视图
@require_GET
def api_dividend_list(request):
    """
    API: 获取分红列表
    """
    query = request.GET.get("q", "")
    limit = int(request.GET.get("limit", 10))

    dividends = StockDividend.objects.all()
    if query:
        dividends = dividends.filter(
            Q(stock_code__icontains=query) | Q(stock_name__icontains=query)
        )

    dividends = dividends.order_by("-registration_date")[:limit]

    data = [
        {
            "stock_code": d.stock_code,
            "stock_name": d.stock_name,
            "dividend_plan": d.dividend_plan,
            "registration_date": (
                d.registration_date.strftime("%Y-%m-%d")
                if d.registration_date
                else None
            ),
            "cash_dividend": d.cash_dividend,
            "share_dividend": d.share_dividend,
            "share_transfer": d.share_transfer,
            "implementation_status": d.implementation_status,
        }
        for d in dividends
    ]

    return JsonResponse({"results": data, "count": len(data)})


@require_GET
def api_financial_report(request):
    """
    API: 获取财务报告
    """
    query = request.GET.get("q", "")
    limit = int(request.GET.get("limit", 10))

    reports = StockFinancialIndicator.objects.all()
    if query:
        reports = reports.filter(Q(stock_code__icontains=query))

    reports = reports.order_by("-report_date")[:limit]

    data = [
        {
            "stock_code": r.stock_code,
            "report_date": (
                r.report_date.strftime("%Y-%m-%d") if r.report_date else None
            ),
            "total_revenue": r.total_revenue,
            "net_profit": r.net_profit,
            "eps": r.eps,
            "roe": r.roe,
            "debt_asset_ratio": r.debt_asset_ratio,
        }
        for r in reports
    ]

    return JsonResponse({"results": data, "count": len(data)})


def stock_financial_indicators(request, stock_code):
    """个股财务指标视图"""
    logger.info(f"开始处理股票 {stock_code} 的财务指标数据")

    # 初始化上下文
    context = {
        "stock_code": stock_code,
        "data_status": "info",
        "data_status_message": None,
    }

    try:
        # 检查是否已有该股票的数据
        existing_data = StockFinancialIndicator.objects.filter(stock_code=stock_code)
        should_fetch = not existing_data.exists()  # 如果没有数据，需要采集

        if not should_fetch:
            # 检查是否有最新数据（假设最新数据不超过3个月）
            latest_data = existing_data.order_by("-report_date").first()
            if latest_data:
                latest_date = latest_data.report_date
                current_date = datetime.now().date()
                days_diff = (current_date - latest_date).days
                should_fetch = days_diff > 90  # 如果最新数据超过90天，需要更新

                if should_fetch:
                    context["data_status"] = "warning"
                    context["data_status_message"] = (
                        f"数据已过期（{days_diff}天），正在更新..."
                    )
                else:
                    context["data_status"] = "success"
                    context["data_status_message"] = (
                        f"数据已是最新（{days_diff}天前更新）"
                    )
        else:
            context["data_status"] = "info"
            context["data_status_message"] = "首次采集数据，请稍候..."

        # 如果需要采集数据
        if should_fetch:
            logger.info(f"开始采集股票 {stock_code} 的财务指标数据")
            fetcher = FinancialDataFetcher()
            try:
                success, message = fetcher.fetch_and_save(stock_code)
                if not success:
                    logger.error(f"获取股票 {stock_code} 的财务数据失败: {message}")
                    context["data_status"] = "error"
                    context["data_status_message"] = f"数据采集失败：{message}"
                else:
                    context["data_status"] = "complete"
                    context["data_status_message"] = "数据采集完成"
            except Exception as e:
                error_msg = str(e)
                logger.error(f"处理股票 {stock_code} 的财务数据时发生错误: {error_msg}")
                context["data_status"] = "error"
                context["data_status_message"] = f"处理数据时发生错误：{error_msg}"

        # 获取显示全部数据的标志
        show_all = request.GET.get("show_all") == "1"

        # 获取财务指标数据
        indicators = StockFinancialIndicator.objects.filter(
            stock_code=stock_code
        ).order_by("-report_date")
        if not show_all:
            indicators = indicators[:4]  # 只显示最近4个季度

        # 处理单位转换（从元转换为亿元）
        for indicator in indicators:
            # 转换为亿元单位，处理空值情况
            indicator.net_profit_display = (
                float(indicator.net_profit) / 100000000 if indicator.net_profit else 0
            )
            indicator.deducted_net_profit_display = (
                float(indicator.deducted_net_profit) / 100000000
                if indicator.deducted_net_profit
                else 0
            )
            indicator.total_revenue_display = (
                float(indicator.total_revenue) / 100000000
                if indicator.total_revenue
                else 0
            )

        # 获取最后更新时间
        latest_update = indicators.first().last_update if indicators else None

        # 更新上下文
        context.update(
            {
                "indicators": indicators,
                "show_all": show_all,
                "latest_update": latest_update,
            }
        )

    except Exception as e:
        error_msg = str(e)
        logger.error(f"处理股票 {stock_code} 的页面时发生错误: {error_msg}")
        context.update(
            {
                "data_status": "error",
                "data_status_message": f"页面加载错误：{error_msg}",
                "indicators": [],
                "show_all": False,
                "latest_update": None,
            }
        )

    return render(
        request, "financial_analysis/stock_financial_indicators.html", context
    )


def technical_indicator_list(request):
    """技术指标列表视图"""
    # 获取查询参数
    date = request.GET.get("date", "")
    stock_code = request.GET.get("stock_code", "")
    stock_name = request.GET.get("stock_name", "")
    indicator_type = request.GET.get("indicator_type", "")

    # 构建查询
    indicators = TechnicalIndicator.objects.all()

    # 应用过滤条件
    if date:
        indicators = indicators.filter(date=date)
    if stock_code:
        indicators = indicators.filter(stock_code__icontains=stock_code)
    if stock_name:
        indicators = indicators.filter(stock_name__icontains=stock_name)
    if indicator_type:
        indicators = indicators.filter(indicator_type=indicator_type)

    # 排序
    indicators = indicators.order_by("-date", "stock_code")

    # 分页
    paginator = Paginator(indicators, 20)
    page = request.GET.get("page", 1)

    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    # 获取所有可用的指标类型
    indicator_types = (
        TechnicalIndicator.objects.values_list("indicator_type", flat=True)
        .distinct()
        .order_by("indicator_type")
    )

    context = {
        "page_obj": page_obj,
        "date": date,
        "stock_code": stock_code,
        "stock_name": stock_name,
        "indicator_type": indicator_type,
        "indicator_types": indicator_types,
    }

    return render(request, "financial_analysis/technical_indicator_list.html", context)


def technical_indicator_detail(request, pk):
    """技术指标详情视图"""
    indicator = get_object_or_404(TechnicalIndicator, pk=pk)

    # 获取该股票的历史技术指标数据
    history_data = TechnicalIndicator.objects.filter(
        stock_code=indicator.stock_code, indicator_type=indicator.indicator_type
    ).order_by("-date")[:30]

    context = {
        "indicator": indicator,
        "history_data": history_data,
    }

    return render(
        request, "financial_analysis/technical_indicator_detail.html", context
    )


def industry_chain_list(request):
    """产业链列表视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    date_str = request.GET.get("date", "")
    sort_by = request.GET.get("sort", "-change_percent")

    # 获取可用日期列表
    available_dates = (
        StockIndustryChain.objects.values_list("date", flat=True)
        .distinct()
        .order_by("-date")
    )

    # 构建查询
    chains = StockIndustryChain.objects.all()

    if search_query:
        chains = chains.filter(
            Q(chain_code__icontains=search_query)
            | Q(chain_name__icontains=search_query)
        )

    selected_date = None
    if date_str:
        try:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            chains = chains.filter(date=selected_date)
        except ValueError:
            pass
    else:
        # 如果没有指定日期，使用最新日期
        if available_dates.exists():
            selected_date = available_dates.first()
            chains = chains.filter(date=selected_date)

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        chains = chains.order_by(f"-{sort_field}")
    else:
        chains = chains.order_by(sort_by)

    # 分页
    paginator = Paginator(chains, 10)
    page_number = request.GET.get("page", 1)
    page_obj = paginator.get_page(page_number)

    # 处理总市值，将其从元转换为亿元
    for item in page_obj:
        if item.total_market_value is not None:
            item.total_market_value = item.total_market_value / 100000000

    context = {
        "page_obj": page_obj,
        "total_items": chains.count(),
        "search_query": search_query,
        "available_dates": available_dates[:30],
        "selected_date": selected_date,
        "sort_by": sort_by,
    }

    return render(request, "financial_analysis/industry_chain_list.html", context)


def industry_chain_detail(request, chain_code):
    """产业链详情视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    sort_by = request.GET.get("sort", "-change_percent")
    page = request.GET.get("page", 1)
    date = request.GET.get("date", "")

    # 获取产业链信息
    chain = get_object_or_404(StockIndustryChain, chain_code=chain_code)

    # 构建查询
    queryset = StockIndustryChain.objects.filter(chain_code=chain_code)

    # 应用搜索过滤
    if search_query:
        queryset = queryset.filter(
            Q(stock_code__icontains=search_query)
            | Q(stock_name__icontains=search_query)
        )

    # 应用日期过滤
    if date:
        queryset = queryset.filter(date=date)

    # 应用排序
    if sort_by in [
        "stock_code",
        "-stock_code",
        "stock_name",
        "-stock_name",
        "change_percent",
        "-change_percent",
    ]:
        queryset = queryset.order_by(sort_by)
    else:
        queryset = queryset.order_by("-change_percent")

    # 分页
    paginator = Paginator(queryset, 20)
    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        "chain": chain,
        "page_obj": page_obj,
        "search_query": search_query,
        "sort_by": sort_by,
        "date": date,
    }

    return render(request, "financial_analysis/industry_chain_detail.html", context)


from django.core.cache import cache
from django.db.models import Window, F, Max
from django.db.models.functions import RowNumber
import time


class FinancialScreenerView(TemplateView):
    template_name = "financial_analysis/financial_screener.html"

    def get_filter_params(self):
        """获取所有筛选参数"""
        return {
            # 基本筛选
            'industry': self.request.GET.get("industry", ""),
            'market_cap_min': self.request.GET.get("market_cap_min", ""),
            'market_cap_max': self.request.GET.get("market_cap_max", ""),
            'search': self.request.GET.get("search", ""),

            # 估值指标
            'pe_min': self.request.GET.get("pe_min", ""),
            'pe_max': self.request.GET.get("pe_max", ""),
            'pb_min': self.request.GET.get("pb_min", ""),
            'pb_max': self.request.GET.get("pb_max", ""),

            # 盈利能力
            'roe_min': self.request.GET.get("roe_min", ""),
            'roe_max': self.request.GET.get("roe_max", ""),
            'roa_min': self.request.GET.get("roa_min", ""),
            'roa_max': self.request.GET.get("roa_max", ""),
            'net_profit_margin_min': self.request.GET.get("net_profit_margin_min", ""),
            'net_profit_margin_max': self.request.GET.get("net_profit_margin_max", ""),
            'gross_profit_margin_min': self.request.GET.get("gross_profit_margin_min", ""),
            'gross_profit_margin_max': self.request.GET.get("gross_profit_margin_max", ""),

            # 成长性
            'net_profit_growth_min': self.request.GET.get("net_profit_growth_min", ""),
            'net_profit_growth_max': self.request.GET.get("net_profit_growth_max", ""),
            'revenue_growth_min': self.request.GET.get("revenue_growth_min", ""),
            'revenue_growth_max': self.request.GET.get("revenue_growth_max", ""),

            # 财务健康度
            'debt_ratio_min': self.request.GET.get("debt_ratio_min", ""),
            'debt_ratio_max': self.request.GET.get("debt_ratio_max", ""),
            'current_ratio_min': self.request.GET.get("current_ratio_min", ""),
            'current_ratio_max': self.request.GET.get("current_ratio_max", ""),

            # 技术指标
            'price_change_min': self.request.GET.get("price_change_min", ""),
            'price_change_max': self.request.GET.get("price_change_max", ""),
            'volume_ratio_min': self.request.GET.get("volume_ratio_min", ""),
            'volume_ratio_max': self.request.GET.get("volume_ratio_max", ""),
            'turnover_rate_min': self.request.GET.get("turnover_rate_min", ""),
            'turnover_rate_max': self.request.GET.get("turnover_rate_max", ""),

            # 特殊筛选
            'continuous_roe': self.request.GET.get("continuous_roe", ""),
            'continuous_growth': self.request.GET.get("continuous_growth", ""),
            'dividend_years': self.request.GET.get("dividend_years", ""),

            # 其他
            'float_market_cap_min': self.request.GET.get("float_market_cap_min", ""),
            'float_market_cap_max': self.request.GET.get("float_market_cap_max", ""),
        }

    def get_preset_filters(self, preset):
        """获取预设策略的筛选条件"""
        presets = {
            'value_stocks': {
                'pe_max': '15',
                'pb_max': '2',
                'roe_min': '10',
                'debt_ratio_max': '50',
                'dividend_years': '3',
            },
            'growth_stocks': {
                'revenue_growth_min': '20',
                'net_profit_growth_min': '25',
                'roe_min': '15',
                'pe_max': '30',
            },
            'quality_stocks': {
                'continuous_roe': '3',
                'roe_min': '15',
                'debt_ratio_max': '40',
                'current_ratio_min': '1.5',
                'net_profit_margin_min': '10',
            },
            'dividend_stocks': {
                'dividend_years': '5',
                'roe_min': '8',
                'debt_ratio_max': '60',
                'pe_max': '20',
            },
            'small_cap_growth': {
                'market_cap_max': '100',
                'revenue_growth_min': '30',
                'net_profit_growth_min': '35',
                'roe_min': '20',
            },
            'undervalued_stocks': {
                'pe_max': '12',
                'pb_max': '1.5',
                'roe_min': '12',
                'net_profit_margin_min': '8',
            }
        }
        return presets.get(preset, {})

    def get_context_data(self, **kwargs):
        # 记录开始时间，用于性能分析
        start_time = time.time()
        context = super().get_context_data(**kwargs)

        # 获取筛选参数
        filters = self.get_filter_params()

        # 获取预设策略参数
        preset = self.request.GET.get("preset", "")
        if preset:
            preset_filters = self.get_preset_filters(preset)
            # 只更新空值的参数，保留用户自定义的参数
            for key, value in preset_filters.items():
                if not filters.get(key):
                    filters[key] = value

        # 记录参数获取时间
        params_time = time.time()

        # 使用缓存获取最新交易日期
        latest_trade_date = cache.get("latest_trade_date")
        if not latest_trade_date:
            latest_trade_date_obj = (
                StockDailyQuote.objects.order_by("-trade_date")
                .values("trade_date")
                .first()
            )
            if latest_trade_date_obj:
                latest_trade_date = latest_trade_date_obj["trade_date"]
                # 缓存24小时
                cache.set("latest_trade_date", latest_trade_date, 60 * 60 * 24)
            else:
                from datetime import date

                latest_trade_date = date.today()

        # 记录获取最新交易日期的时间
        trade_date_time = time.time()
        logger.info(f"\n获取最新交易日期耗时: {trade_date_time - params_time:.4f}秒")

        # 使用缓存获取最新财报期
        latest_report_date = cache.get("latest_report_date")
        if not latest_report_date:
            latest_report_date_obj = (
                StockFinancialIndicator.objects.order_by("-report_date")
                .values("report_date")
                .first()
            )
            if latest_report_date_obj:
                latest_report_date = latest_report_date_obj["report_date"]
                # 缓存24小时
                cache.set("latest_report_date", latest_report_date, 60 * 60 * 24)
            else:
                from datetime import date

                latest_report_date = date.today()

        # 记录获取最新财报期的时间
        report_date_time = time.time()
        logger.info(f"\n获取最新财报期耗时: {report_date_time - trade_date_time:.4f}秒")

        # 首先获取基本股票信息
        basic_query = Q()
        if filters['industry']:
            basic_query &= Q(industry=filters['industry'])
        if filters['search']:
            basic_query &= Q(stock_code__icontains=filters['search']) | Q(
                stock_name__icontains=filters['search']
            )

        stocks_basic = StockBasic.objects.filter(basic_query)

        # 获取股票代码列表
        stock_codes = list(stocks_basic.values_list("stock_code", flat=True))

        # 获取行业平均PE
        # 使用缓存获取行业平均PE
        cache_key = f"industry_avg_pe_{latest_trade_date}"
        industry_avg_pe = cache.get(cache_key)

        if not industry_avg_pe:
            industry_avg_pe = {}

            # 首先获取所有行业
            all_industries = (
                StockBasic.objects.exclude(industry__isnull=True)
                .exclude(industry="")
                .values_list("industry", flat=True)
                .distinct()
            )
            industry_list = list(all_industries)

            # 如果缓存中没有数据，才进行计算
            # 首先尝试使用数据库级别的聚合查询
            try:
                # 使用原始 SQL 查询来提高效率
                from django.db import connection

                with connection.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT b.industry, AVG(q.pe_ratio) as avg_pe, COUNT(*) as count
                        FROM stock_daily_quote q
                        JOIN stock_basic b ON q.stock_code = b.stock_code
                        WHERE q.trade_date = %s
                          AND q.pe_ratio IS NOT NULL
                          AND q.pe_ratio > 0
                          AND q.pe_ratio < 200
                          AND b.industry IS NOT NULL
                          AND b.industry != ''
                        GROUP BY b.industry
                    """,
                        [latest_trade_date],
                    )

                    for row in cursor.fetchall():
                        industry_name, avg_pe, count = row
                        if industry_name and avg_pe:
                            industry_avg_pe[industry_name] = float(avg_pe)

                # 如果没有数据，使用预定义的行业平均PE
                if not industry_avg_pe:
                    # 预定义的行业平均PE值
                    default_industry_pe = {
                        "银行": 5.5,  # 银行
                        "房地产开发": 8.2,  # 房地产开发
                        "软件开发": 45.3,  # 软件开发
                        "电子设备和仪器": 32.1,  # 电子设备和仪器
                        "医药制造": 38.7,  # 医药制造
                        "汽车制造": 15.2,  # 汽车制造
                        "食品饮料": 25.6,  # 食品饮料
                        "电信运营": 12.8,  # 电信运营
                        "互联网服务": 30.5,  # 互联网服务
                        "电力设备": 18.3,  # 电力设备
                        "建筑建材": 14.7,  # 建筑建材
                    }

                    # 将预定义的行业PE值添加到结果中
                    for industry in industry_list:
                        if industry in default_industry_pe:
                            industry_avg_pe[industry] = default_industry_pe[industry]
                        else:
                            # 对于没有预定义的行业，使用默认值
                            industry_avg_pe[industry] = 20.0  # 默认市场PE

                # 缓存24小时
                cache.set(cache_key, industry_avg_pe, 60 * 60 * 24)

            except Exception as e:
                # 如果发生错误，回退到默认值
                logger.error(f"Error calculating industry PE: {e}")
                # 默认行业PE值
                default_pe = {
                    "银行": 5.5,
                    "房地产开发": 8.2,
                    "软件开发": 45.3,
                    "电子设备和仪器": 32.1,
                    "医药制造": 38.7,
                    "汽车制造": 15.2,
                    "食品饮料": 25.6,
                    "电信运营": 12.8,
                    "互联网服务": 30.5,
                    "电力设备": 18.3,
                    "建筑建材": 14.7,
                }

                # 将默认行业PE值添加到结果中
                for industry in industry_list:
                    if industry in default_pe:
                        industry_avg_pe[industry] = default_pe[industry]
                    else:
                        industry_avg_pe[industry] = 20.0  # 默认市场PE

                # 缓存24小时
                cache.set(cache_key, industry_avg_pe, 60 * 60 * 24)

        # 记录获取行业平均PE的时间
        industry_pe_time = time.time()
        logger.info(
            f"\n获取行业平均PE耗时: {industry_pe_time - report_date_time:.4f}秒"
        )

        # 获取每日行情数据
        daily_query = Q(stock_code__in=stock_codes, trade_date=latest_trade_date)
        if filters['pe_min']:
            daily_query &= Q(pe_ratio__gte=float(filters['pe_min']))
        if filters['pe_max']:
            daily_query &= Q(pe_ratio__lte=float(filters['pe_max']))
        if filters['pb_min']:
            daily_query &= Q(pb_ratio__gte=float(filters['pb_min']))
        if filters['pb_max']:
            daily_query &= Q(pb_ratio__lte=float(filters['pb_max']))
        if filters['market_cap_min']:
            daily_query &= Q(
                total_value__gte=float(filters['market_cap_min']) * 100000000
            )  # 转换为元
        if filters['market_cap_max']:
            daily_query &= Q(
                total_value__lte=float(filters['market_cap_max']) * 100000000
            )  # 转换为元

        daily_quotes = StockDailyQuote.objects.filter(daily_query)
        daily_data = {}
        for quote in daily_quotes:
            daily_data[quote.stock_code] = {
                "pe_ratio": quote.pe_ratio,
                "pb_ratio": quote.pb_ratio,
                "market_cap": (
                    quote.total_value / 100000000 if quote.total_value else None
                ),  # 转换为亿元
                "float_market_cap": (
                    quote.float_value / 100000000 if quote.float_value else None
                ),  # 转换为亿元
                "latest_price": quote.close_price,
                "change_percent": quote.change_percent,
            }

        # 获取财务指标数据
        # 不使用最新财报期，而是获取所有股票的最新财务数据
        financial_query = Q(stock_code__in=stock_codes)

        # 打印调试信息
        # logger.info(f"\n查询条件: {financial_query}")
        # logger.info(f"最新财报期: {latest_report_date}")

        if filters['roe_min']:
            financial_query &= Q(roe__gte=float(filters['roe_min']))
        if filters['roe_max']:
            financial_query &= Q(roe__lte=float(filters['roe_max']))
        if filters['net_profit_growth_min']:
            financial_query &= Q(net_profit_growth__gte=float(filters['net_profit_growth_min']))
        if filters['net_profit_growth_max']:
            financial_query &= Q(net_profit_growth__lte=float(filters['net_profit_growth_max']))
        if filters['revenue_growth_min']:
            financial_query &= Q(total_revenue_growth__gte=float(filters['revenue_growth_min']))
        if filters['revenue_growth_max']:
            financial_query &= Q(total_revenue_growth__lte=float(filters['revenue_growth_max']))

        # 获取每个股票的最新财务数据
        # 记录开始时间
        financial_start_time = time.time()

        # 使用子查询获取每支股票的最新财务数据，避免循环查询
        from django.db import connection

        financial_data = {}

        # 使用原生 SQL 查询获取每支股票的最新财务数据
        with connection.cursor() as cursor:
            cursor.execute(
                """
                WITH LatestReports AS (
                    SELECT
                        stock_code,
                        MAX(report_date) as latest_date
                    FROM
                        stock_financial_indicator
                    WHERE
                        stock_code IN %s
                    GROUP BY
                        stock_code
                )
                SELECT
                    f.stock_code,
                    f.report_date,
                    f.roe,
                    f.roa,
                    f.net_profit_growth,
                    f.total_revenue_growth,
                    f.gross_profit_margin,
                    f.net_profit_margin,
                    f.debt_asset_ratio,
                    f.current_ratio
                FROM
                    stock_financial_indicator f
                JOIN
                    LatestReports lr
                ON
                    f.stock_code = lr.stock_code AND f.report_date = lr.latest_date
                """,
                [tuple(stock_codes) if len(stock_codes) > 0 else ("",)],
            )

            # 将查询结果转换为字典
            for row in cursor.fetchall():
                (
                    stock_code,
                    report_date,
                    roe,
                    roa,
                    net_profit_growth,
                    total_revenue_growth,
                    gross_profit_margin,
                    net_profit_margin,
                    debt_asset_ratio,
                    current_ratio,
                ) = row
                financial_data[stock_code] = {
                    "roe": float(roe) if roe is not None else None,
                    "roa": float(roa) if roa is not None else None,
                    "net_profit_growth": (
                        float(net_profit_growth)
                        if net_profit_growth is not None
                        else None
                    ),
                    "revenue_growth": (
                        float(total_revenue_growth)
                        if total_revenue_growth is not None
                        else None
                    ),
                    "gross_profit_margin": (
                        float(gross_profit_margin)
                        if gross_profit_margin is not None
                        else None
                    ),
                    "net_profit_margin": (
                        float(net_profit_margin)
                        if net_profit_margin is not None
                        else None
                    ),
                    "debt_ratio": (
                        float(debt_asset_ratio)
                        if debt_asset_ratio is not None
                        else None
                    ),
                    "current_ratio": (
                        float(current_ratio)
                        if current_ratio is not None
                        else None
                    ),
                    "report_date": report_date,
                }

        # 记录获取最新财务数据的时间
        financial_end_time = time.time()
        logger.info(
            f"\n获取最新财务数据耗时: {financial_end_time - financial_start_time:.4f}秒"
        )

        # 打印调试信息
        # if stock_code == "000001":
        #     logger.info(
        #         f"\n样本财务数据: {stock_code} - {latest_financial.report_date}"
        #     )
        #     logger.info(f"ROE: {latest_financial.roe}")
        #     logger.info(f"净利润增长率: {latest_financial.net_profit_growth}")
        #     logger.info(f"营收增长率: {latest_financial.total_revenue_growth}")
        # 合并数据
        combined_stocks = []
        for stock in stocks_basic:
            stock_data = {
                "stock_code": stock.stock_code,
                "stock_name": stock.stock_name,
                "industry": stock.industry,
                "industry_avg_pe": (
                    industry_avg_pe.get(stock.industry, None)
                    if stock.industry
                    else None
                ),
                "continuous_roe": False,  # 默认为否
            }

            # 添加每日行情数据
            if stock.stock_code in daily_data:
                stock_data.update(daily_data[stock.stock_code])
            else:
                stock_data.update(
                    {
                        "pe_ratio": None,
                        "pb_ratio": None,
                        "market_cap": None,
                        "latest_price": None,
                        "change_percent": None,
                    }
                )

            # 添加财务指标数据
            if stock.stock_code in financial_data:
                stock_data.update(financial_data[stock.stock_code])

                # 如果选择了连续3年ROE > 15%的筛选条件
                if filters['continuous_roe']:
                    # 查询该股票过去3年的ROE数据
                    with connection.cursor() as cursor:
                        cursor.execute(
                            """
                            SELECT report_date, roe
                            FROM stock_financial_indicator
                            WHERE stock_code = %s
                            AND report_date <= %s
                            AND report_date >= DATE_SUB(%s, INTERVAL 3 YEAR)
                            AND report_date LIKE '____1231'  -- 只选择年报
                            ORDER BY report_date DESC
                            LIMIT 3
                            """,
                            [stock.stock_code, latest_report_date, latest_report_date],
                        )

                        roe_data = cursor.fetchall()
                        # 如果有至少3年的数据，并且每年ROE都 > 15%
                        if len(roe_data) >= 3 and all(
                            float(r[1] or 0) > 15 for r in roe_data
                        ):
                            stock_data["continuous_roe"] = True
                # 打印调试信息
                # if stock.stock_code == "000001":
                #     logger.info(
                #         f"\n样本股票财务数据: {stock.stock_code} - {stock.stock_name}"
                #     )
                #     logger.info(f"ROE: {stock_data['roe']}")
                #     logger.info(f"净利润增长率: {stock_data['net_profit_growth']}")
                #     logger.info(f"营收增长率: {stock_data['revenue_growth']}")
            else:
                stock_data.update(
                    {
                        "roe": None,
                        "roa": None,
                        "net_profit_growth": None,
                        "revenue_growth": None,
                        "gross_profit_margin": None,
                        "net_profit_margin": None,
                        "debt_ratio": None,
                        "current_ratio": None,
                        "report_date": None,
                    }
                )

            combined_stocks.append(stock_data)

        # 应用筛选条件
        filtered_stocks = []
        for stock in combined_stocks:
            include = True

            # 使用通用函数检查数值范围筛选条件
            numeric_checks = [
                ('pe_min', 'pe_ratio', 'gte'),
                ('pe_max', 'pe_ratio', 'lte'),
                ('pb_min', 'pb_ratio', 'gte'),
                ('pb_max', 'pb_ratio', 'lte'),
                ('market_cap_min', 'market_cap', 'gte'),
                ('market_cap_max', 'market_cap', 'lte'),
                ('roe_min', 'roe', 'gte'),
                ('roe_max', 'roe', 'lte'),
                ('roa_min', 'roa', 'gte'),
                ('roa_max', 'roa', 'lte'),
                ('net_profit_growth_min', 'net_profit_growth', 'gte'),
                ('net_profit_growth_max', 'net_profit_growth', 'lte'),
                ('revenue_growth_min', 'revenue_growth', 'gte'),
                ('revenue_growth_max', 'revenue_growth', 'lte'),
                ('gross_profit_margin_min', 'gross_profit_margin', 'gte'),
                ('gross_profit_margin_max', 'gross_profit_margin', 'lte'),
                ('net_profit_margin_min', 'net_profit_margin', 'gte'),
                ('net_profit_margin_max', 'net_profit_margin', 'lte'),
                ('debt_ratio_min', 'debt_ratio', 'gte'),
                ('debt_ratio_max', 'debt_ratio', 'lte'),
                ('current_ratio_min', 'current_ratio', 'gte'),
                ('current_ratio_max', 'current_ratio', 'lte'),
                ('float_market_cap_min', 'float_market_cap', 'gte'),
                ('float_market_cap_max', 'float_market_cap', 'lte'),
            ]

            for filter_key, stock_key, operator in numeric_checks:
                filter_value = filters.get(filter_key)
                if filter_value:
                    try:
                        filter_float = float(filter_value)
                        stock_value = stock.get(stock_key)
                        if stock_value is None:
                            include = False
                            break
                        elif operator == 'gte' and stock_value < filter_float:
                            include = False
                            break
                        elif operator == 'lte' and stock_value > filter_float:
                            include = False
                            break
                    except ValueError:
                        continue

            # 特殊筛选条件
            if filters['continuous_roe'] and not stock["continuous_roe"]:
                include = False

            if include:
                filtered_stocks.append(stock)

        # 排序
        filtered_stocks.sort(key=lambda x: x["stock_code"])

        # 获取排序参数
        sort_by = self.request.GET.get("sort", "stock_code")
        sort_order = self.request.GET.get("order", "asc")

        # 定义有效的排序字段
        valid_sort_fields = {
            "stock_code": "stock_code",
            "stock_name": "stock_name",
            "industry": "industry",
            "market_cap": "market_cap",
            "float_market_cap": "float_market_cap",
            "pe_ratio": "pe_ratio",
            "industry_avg_pe": "industry_avg_pe",
            "pb_ratio": "pb_ratio",
            "latest_price": "latest_price",
            "change_percent": "change_percent",
            "roe": "roe",
            "roa": "roa",
            "net_profit_growth": "net_profit_growth",
            "revenue_growth": "revenue_growth",
            "gross_profit_margin": "gross_profit_margin",
            "net_profit_margin": "net_profit_margin",
            "debt_ratio": "debt_ratio",
            "current_ratio": "current_ratio",
        }

        # 验证排序字段
        if sort_by not in valid_sort_fields:
            sort_by = "stock_code"

        # 排序函数
        def sort_key(item):
            value = item[valid_sort_fields[sort_by]]
            # 处理None值
            if value is None:
                return float("-inf") if sort_order == "asc" else float("inf")
            return value

        # 应用排序
        filtered_stocks.sort(key=sort_key, reverse=(sort_order == "desc"))

        # 分页
        page = self.request.GET.get("page", 1)
        rows_per_page = self.request.GET.get("rows_per_page", 10)
        try:
            rows_per_page = int(rows_per_page)
            if rows_per_page not in [10, 20, 50, 100]:
                rows_per_page = 10
        except (ValueError, TypeError):
            rows_per_page = 10

        paginator = Paginator(filtered_stocks, rows_per_page)
        page_obj = paginator.get_page(page)

        # 获取行业列表
        industries = (
            StockBasic.objects.values_list("industry", flat=True)
            .distinct()
            .order_by("industry")
        )
        # 添加预设策略信息
        presets = {
            'value_stocks': {
                'name': '价值股筛选',
                'description': '低估值、高分红、财务稳健的价值股',
                'icon': 'ti-shield-check'
            },
            'growth_stocks': {
                'name': '成长股筛选',
                'description': '高成长、高ROE的成长股',
                'icon': 'ti-trending-up'
            },
            'quality_stocks': {
                'name': '优质股筛选',
                'description': '连续盈利、ROE稳定的优质股',
                'icon': 'ti-star'
            },
            'dividend_stocks': {
                'name': '分红股筛选',
                'description': '高股息率、连续分红的股票',
                'icon': 'ti-gift'
            },
            'small_cap_growth': {
                'name': '小盘成长股',
                'description': '市值较小但成长性强的股票',
                'icon': 'ti-rocket'
            },
            'undervalued_stocks': {
                'name': '低估值股票',
                'description': '严重低估的价值股',
                'icon': 'ti-discount'
            }
        }

        context.update(
            {
                "page_obj": page_obj,
                "industries": industries,
                "filters": filters,
                "presets": presets,
                "current_preset": preset,
                "latest_trade_date": latest_trade_date,
                "latest_report_date": latest_report_date,
                "sort_by": sort_by,
                "sort_order": sort_order,
                "rows_per_page": rows_per_page,
                "total_count": len(filtered_stocks),
            }
        )

        # 记录总耗时
        end_time = time.time()
        total_time = end_time - start_time
        logger.info(f"\n总耗时: {total_time:.4f}秒")

        # 性能优化总结：
        # 1. 使用缓存获取最新交易日期和财报期
        # 2. 使用缓存获取行业平均PE
        # 3. 使用原生 SQL 查询获取每支股票的最新财务数据，避免循环查询
        # 4. 使用数据库级别的聚合查询计算行业平均PE
        # 5. 添加性能日志，记录各个环节的耗时

        return context


@csrf_exempt
@login_required
@require_POST
def batch_favorite(request):
    """批量收藏股票"""
    try:
        # 打印请求信息以便调试
        print(f"Request method: {request.method}")
        print(f"Request content type: {request.content_type}")

        # 判断请求类型
        if request.content_type == "application/json":
            # AJAX请求
            print(f"Request body: {request.body.decode('utf-8')}")
            data = json.loads(request.body)
            stocks = data.get("stocks", [])
        else:
            # 表单提交
            selected_stocks = request.POST.get("selected_stocks", "")
            try:
                stocks = json.loads(selected_stocks)
            except json.JSONDecodeError:
                stocks = []

        print(f"Parsed stocks: {stocks}")

        if not stocks:
            return JsonResponse({"success": False, "error": "未选择股票"})

        # 批量创建收藏记录
        count = 0
        for stock in stocks:
            stock_code = stock.get("code")
            stock_name = stock.get("name")

            print(f"Processing stock: {stock_code} - {stock_name}")

            if not stock_code or not stock_name:
                continue

            # 使用get_or_create避免重复收藏
            favorite, created = StockFavorite.objects.get_or_create(
                user=request.user,
                stock_code=stock_code,
                defaults={"stock_name": stock_name},
            )

            print(
                f"Stock {stock_code} - {stock_name}: {'created new' if created else 'already exists'}"
            )

            if created:
                count += 1

        result = {
            "success": True,
            "count": count,
            "message": f"成功收藏 {count} 只股票",
        }
        print(f"Result: {result}")
        return JsonResponse(result)
    except Exception as e:
        error_msg = str(e)
        print(f"Error: {error_msg}")
        return JsonResponse({"success": False, "error": error_msg})


@login_required
def favorite_list(request):
    """收藏列表视图"""
    favorites = StockFavorite.objects.filter(user=request.user).order_by("-create_time")

    # 分页处理
    paginator = Paginator(favorites, 20)
    page = request.GET.get("page", 1)
    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        "favorites": page_obj,
        "page_obj": page_obj,
        "total_count": favorites.count(),
    }

    return render(request, "financial_analysis/favorite_list.html", context)


@login_required
@require_GET
def get_favorite_stocks(request):
    """获取当前用户收藏的股票列表"""
    try:
        favorites = StockFavorite.objects.filter(user=request.user).values_list(
            "stock_code", flat=True
        )
        return JsonResponse({"success": True, "favorites": list(favorites)})
    except Exception as e:
        return JsonResponse({"success": False, "error": str(e)})


@login_required
@require_POST
def remove_favorite(request, favorite_id):
    """取消收藏"""
    try:
        favorite = get_object_or_404(StockFavorite, id=favorite_id, user=request.user)
        favorite.delete()
        return JsonResponse({"success": True})
    except Exception as e:
        logger.error(f"取消收藏失败: {str(e)}")
        return JsonResponse({"success": False, "error": str(e)})


@login_required
def add_favorite(request, stock_code, stock_name):
    """添加单个股票到收藏"""
    try:
        # 使用get_or_create避免重复收藏
        favorite, created = StockFavorite.objects.get_or_create(
            user=request.user,
            stock_code=stock_code,
            defaults={"stock_name": stock_name},
        )

        if created:
            messages.success(request, f"成功收藏 {stock_name}({stock_code})")
        else:
            messages.info(request, f"{stock_name}({stock_code}) 已经在收藏列表中")

        # 重定向到原页面
        referer = request.META.get("HTTP_REFERER", "/")
        return redirect(referer)
    except Exception as e:
        messages.error(request, f"收藏失败: {str(e)}")
        return redirect("financial_analysis:financial_screener")


@login_required
@require_POST
@csrf_exempt
def ajax_add_favorite(request):
    """异步添加股票到收藏"""
    try:
        data = json.loads(request.body)
        stock_code = data.get("stock_code")
        stock_name = data.get("stock_name")

        if not stock_code or not stock_name:
            return JsonResponse({"success": False, "message": "股票代码或名称不能为空"})

        # 使用get_or_create避免重复收藏
        favorite, created = StockFavorite.objects.get_or_create(
            user=request.user,
            stock_code=stock_code,
            defaults={"stock_name": stock_name},
        )

        if created:
            message = f"成功收藏 {stock_name}({stock_code})"
        else:
            message = f"{stock_name}({stock_code}) 已经在收藏列表中"

        return JsonResponse(
            {
                "success": True,
                "created": created,
                "message": message,
                "stock_code": stock_code,
            }
        )
    except Exception as e:
        return JsonResponse({"success": False, "message": f"收藏失败: {str(e)}"})
