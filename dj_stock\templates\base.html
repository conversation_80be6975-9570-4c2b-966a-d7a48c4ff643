{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>{% block title %}股票数据分析系统{% endblock %}</title>
    <!-- CSS files -->

    <link href="{% static 'css/tabler.min.css' %}" rel="stylesheet" />
    <link rel="stylesheet" href="{% static 'css/tabler-icons.min.css' %}" />
    <link href="{% static 'css/tabler-flags.min.css' %}" rel="stylesheet" />
    <link href="{% static 'css/tabler-payments.min.css' %}" rel="stylesheet" />
    <link href="{% static 'css/tabler-vendors.min.css' %}" rel="stylesheet" />
    <link href="{% static 'css/custom.css' %}" rel="stylesheet" />
    <!-- 自定义CSS已移至external/custom.css -->
    {% block extra_css %}{% endblock %}
  </head>
  <body class="theme-light">
    <div class="page">
      <!-- 侧边栏 -->
      <aside class="navbar navbar-vertical navbar-expand-lg">
        <div class="container-fluid">
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark">
            <a href="{% url 'market_data:index' %}">
              <span class="d-flex align-items-center">
                <i class="ti ti-chart-line me-2"></i>
                股票数据分析
              </span>
            </a>
          </h1>
          <div class="collapse navbar-collapse" id="navbar-menu">
            <ul class="navbar-nav pt-lg-3">
              <!-- 首页 -->
              <li class="nav-item">
                <a class="nav-link" href="{% url 'market_data:index' %}">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-home"></i>
                  </span>
                  <span class="nav-link-title">首页</span>
                </a>
              </li>

              <!-- 市场监控 -->
              <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#market-monitor">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-device-analytics"></i>
                  </span>
                  <span class="nav-link-title">市场监控</span>
                  <span class="nav-link-toggle"></span>
                </a>
                <div class="collapse" id="market-monitor">
                  <ul class="nav nav-sm flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:market_index_list' %}">
                        <i class="ti ti-chart-dots me-2"></i>
                        大盘指数
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:market_fund_flow' %}">
                        <i class="ti ti-cash me-2"></i>
                        市场资金流向
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:sector_fund_flow' %}">
                        <i class="ti ti-building me-2"></i>
                        行业资金流向
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:limit_list' %}">
                        <i class="ti ti-arrow-up-down me-2"></i>
                        涨跌停数据
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:data_statistics' %}">
                        <i class="ti ti-chart-area me-2"></i>
                        数据统计
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- 股票筛选 -->
              <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#stock-screening">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-filter"></i>
                  </span>
                  <span class="nav-link-title">股票筛选</span>
                  <span class="nav-link-toggle"></span>
                </a>
                <div class="collapse" id="stock-screening">
                  <ul class="nav nav-sm flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:stock_list' %}">
                        <i class="ti ti-list me-2"></i>
                        个股行情
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:financial_screener' %}">
                        <i class="ti ti-filter-star me-2"></i>
                        智能选股筛选器
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:industry_board_list' %}">
                        <i class="ti ti-building me-2"></i>
                        行业板块
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:board_concept' %}">
                        <i class="ti ti-bulb me-2"></i>
                        概念板块
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:industry_chain_list' %}">
                        <i class="ti ti-hierarchy me-2"></i>
                        产业链
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- 基本面分析 -->
              <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#fundamental-analysis">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-report-money"></i>
                  </span>
                  <span class="nav-link-title">基本面分析</span>
                  <span class="nav-link-toggle"></span>
                </a>
                <div class="collapse" id="fundamental-analysis">
                  <ul class="nav nav-sm flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:financial_report_list' %}">
                        <i class="ti ti-file-report me-2"></i>
                        财务数据
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:financial_indicator' '000001' %}">
                        <i class="ti ti-chart-bar me-2"></i>
                        财务指标
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:dividend_list' %}">
                        <i class="ti ti-coin me-2"></i>
                        分红派息
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:industry_pe_list' %}">
                        <i class="ti ti-chart-pie me-2"></i>
                        行业市盈率
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:shareholder_statistics' '000001' %}">
                        <i class="ti ti-users me-2"></i>
                        股东统计
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:shareholder_detail_list' %}">
                        <i class="ti ti-user-circle me-2"></i>
                        股东持股明细
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- 技术面分析 -->
              <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#technical-analysis">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-chart-line"></i>
                  </span>
                  <span class="nav-link-title">技术面分析</span>
                  <span class="nav-link-toggle"></span>
                </a>
                <div class="collapse" id="technical-analysis">
                  <ul class="nav nav-sm flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'financial_analysis:technical_indicator_list' %}">
                        <i class="ti ti-chart-line-filled me-2"></i>
                        技术指标
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:stock_fund_flow' %}">
                        <i class="ti ti-cash me-2"></i>
                        个股资金流向
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- 市场情报 -->
              <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#market-intelligence">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-file-analytics"></i>
                  </span>
                  <span class="nav-link-title">市场情报</span>
                  <span class="nav-link-toggle"></span>
                </a>
                <div class="collapse" id="market-intelligence">
                  <ul class="nav nav-sm flex-column">
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:top_list' %}">
                        <i class="ti ti-award me-2"></i>
                        龙虎榜
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:margin_list' %}">
                        <i class="ti ti-arrows-exchange me-2"></i>
                        融资融券
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:comment_list' %}">
                        <i class="ti ti-star me-2"></i>
                        千股千评
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:active_broker_list' %}">
                        <i class="ti ti-building-bank me-2"></i>
                        活跃营业部
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:hk_stock_connect' %}">
                        <i class="ti ti-world me-2"></i>
                        港股通成份股
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:ipo_list' %}">
                        <i class="ti ti-rocket me-2"></i>
                        新股申购
                      </a>
                    </li>
                    <li class="nav-item">
                      <a class="nav-link" href="{% url 'market_data:risk_warning' %}">
                        <i class="ti ti-alert-triangle me-2"></i>
                        风险提示
                      </a>
                    </li>
                  </ul>
                </div>
              </li>

              <!-- 我的收藏 -->
              <li class="nav-item">
                <a class="nav-link" href="{% url 'financial_analysis:favorite_list' %}">
                  <span class="nav-link-icon d-md-none d-lg-inline-block">
                    <i class="ti ti-star"></i>
                  </span>
                  <span class="nav-link-title">我的收藏</span>
                </a>
              </li>

              <!-- 底部版权信息 -->
              <li class="nav-item mt-auto">
                <div class="nav-link text-center py-3" style="font-size: 0.8rem">
                  <div>© 2023-2024 股票数据分析系统</div>
                  <div>版本 v1.2</div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </aside>
      <div class="page-wrapper">
        <!-- 顶部导航栏 -->
        <header class="navbar navbar-expand-md navbar-light sticky-top d-print-none">
          <div class="container-xl">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
              <span class="navbar-toggler-icon"></span>
            </button>
            <div class="navbar-nav flex-row order-md-last">
              <div class="d-none d-md-flex me-3">
                <a href="?theme=dark" class="nav-link px-0 hide-theme-dark" title="启用深色模式" data-bs-toggle="tooltip" data-bs-placement="bottom">
                  <i class="ti ti-moon"></i>
                </a>
                <a href="?theme=light" class="nav-link px-0 hide-theme-light" title="启用浅色模式" data-bs-toggle="tooltip" data-bs-placement="bottom">
                  <i class="ti ti-sun"></i>
                </a>
              </div>
              <div class="navbar-nav flex-row ms-auto">
                {% if user.is_authenticated %}
                <div class="nav-item dropdown">
                  <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown">
                    <span class="avatar avatar-sm">{{ user.username|first|upper }}</span>
                  </a>
                  <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                    <a href="#" class="dropdown-item">个人信息</a>
                    <div class="dropdown-divider"></div>
                    <form method="post" action="{% url 'logout' %}" class="d-inline">
                      {% csrf_token %}
                      <button type="submit" class="dropdown-item">退出登录</button>
                    </form>
                  </div>
                </div>
                {% else %}
                <a href="{% url 'login' %}" class="nav-link">登录</a>
                {% endif %}
              </div>
            </div>
            <div class="collapse navbar-collapse" id="navbar-menu">
              <div>
                <form action="{% url 'market_data:stock_list' %}" method="get" class="d-flex">
                  <div class="input-icon">
                    <span class="input-icon-addon">
                      <i class="ti ti-search"></i>
                    </span>
                    <input type="text" name="q" class="form-control" placeholder="搜索股票代码或名称..." />
                  </div>
                  <button type="submit" class="btn ms-2">搜索</button>
                </form>
              </div>
            </div>
          </div>
        </header>
        <!-- 主内容区 -->
        <div class="page-body">
          <div class="container-xl">
            <!-- 消息显示 -->
            {% if messages %}
            <div class="messages">
              {% for message in messages %}
              <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
              </div>
              {% endfor %}
            </div>
            {% endif %} {% block content %}{% endblock %}
          </div>
        </div>
        <!-- 页脚 -->
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
                  <li class="list-inline-item">© 2023-2024 股票数据分析系统</li>
                  <li class="list-inline-item">
                    <a href="javascript:void(0)" class="link-secondary">隐私政策</a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>

    <!-- JS libs -->
    <script src="{% static 'js/tabler.min.js' %}"></script>

    <!-- 主题切换和导航增强 -->
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const themeStorageKey = 'tabler-theme'
        const defaultTheme = 'light'

        // 获取当前主题或使用默认主题
        const getTheme = () => {
          return localStorage.getItem(themeStorageKey) || defaultTheme
        }

        // 设置主题
        const setTheme = (theme) => {
          localStorage.setItem(themeStorageKey, theme)
          document.body.classList.remove('theme-dark', 'theme-light')
          document.body.classList.add(`theme-${theme}`)
        }

        // 初始化主题
        setTheme(getTheme())

        // 为主题切换按钮添加事件监听器
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((element) => {
          element.addEventListener('click', function (e) {
            e.preventDefault()
            const theme = this.href.includes('theme=dark') ? 'dark' : 'light'
            setTheme(theme)
          })
        })

        // 初始化所有工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        tooltipTriggerList.map(function (tooltipTriggerEl) {
          return new bootstrap.Tooltip(tooltipTriggerEl)
        })

        // 导航菜单增强
        function enhanceNavigation() {
          // 获取当前页面路径
          const currentPath = window.location.pathname

          // 找到所有导航链接
          const navLinks = document.querySelectorAll('.navbar-vertical .nav-link')

          // 保存已展开的菜单状态
          function saveMenuState() {
            const expandedMenus = {}
            document.querySelectorAll('.navbar-vertical .collapse').forEach((menu) => {
              if (menu.classList.contains('show')) {
                expandedMenus[menu.id] = true
              }
            })
            localStorage.setItem('expandedMenus', JSON.stringify(expandedMenus))
          }

          // 恢复已展开的菜单状态
          function restoreMenuState() {
            try {
              const expandedMenus = JSON.parse(localStorage.getItem('expandedMenus')) || {}
              Object.keys(expandedMenus).forEach((menuId) => {
                const menu = document.getElementById(menuId)
                if (menu) {
                  menu.classList.add('show')
                }
              })
            } catch (e) {
              console.error('Error restoring menu state:', e)
            }
          }

          // 标记当前页面的菜单项
          navLinks.forEach((link) => {
            // 如果是子菜单链接
            if (!link.hasAttribute('data-bs-toggle')) {
              const href = link.getAttribute('href')
              if (href && (currentPath === href || currentPath.startsWith(href + '/'))) {
                link.classList.add('active')

                // 找到父菜单项并标记
                const parentMenu = link.closest('.collapse')
                if (parentMenu) {
                  parentMenu.classList.add('show')
                  const parentNavItem = parentMenu.closest('.nav-item')
                  if (parentNavItem) {
                    parentNavItem.classList.add('has-active-child')
                  }
                }
              }
            }
          })

          // 为所有菜单折叠按钮添加点击事件
          document.querySelectorAll('.navbar-vertical [data-bs-toggle="collapse"]').forEach((toggle) => {
            toggle.addEventListener('click', function () {
              // 添加视觉反馈
              this.classList.add('menu-toggle-clicked')
              setTimeout(() => {
                this.classList.remove('menu-toggle-clicked')
              }, 300)

              // 延迟一下保存状态，等待折叠动画完成
              setTimeout(saveMenuState, 300)

              // 当菜单展开时，添加高亮效果
              const menuId = this.getAttribute('href')
              const menu = document.querySelector(menuId)
              if (menu && menu.classList.contains('show')) {
                menu.querySelectorAll('.nav-link').forEach((link) => {
                  link.classList.add('menu-item-highlight')
                  setTimeout(() => {
                    link.classList.remove('menu-item-highlight')
                  }, 500)
                })
              }
            })
          })

          // 恢复之前的菜单状态
          restoreMenuState()
        }

        // 执行导航菜单增强
        enhanceNavigation()
      })

      // 移动端侧边栏切换
      document.addEventListener('DOMContentLoaded', function () {
        const navbarToggler = document.querySelector('.navbar-toggler')
        const navbarVertical = document.querySelector('.navbar-vertical')

        if (navbarToggler) {
          navbarToggler.addEventListener('click', function () {
            navbarVertical.classList.toggle('show')
          })
        }

        // 点击页面内容区域时关闭移动端侧边栏
        const pageWrapper = document.querySelector('.page-wrapper')
        if (pageWrapper && window.innerWidth < 992) {
          pageWrapper.addEventListener('click', function () {
            navbarVertical.classList.remove('show')
          })
        }
      })
    </script>

    {% block extra_js %}{% endblock %}
  </body>
</html>
