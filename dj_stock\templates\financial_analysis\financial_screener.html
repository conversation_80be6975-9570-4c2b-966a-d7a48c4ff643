{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<style>
/* 页面整体样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.page-title {
    color: white !important;
    font-weight: 600;
}

.page-subtitle {
    color: rgba(255,255,255,0.9);
}

/* 筛选卡片样式 */
.filter-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.filter-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.filter-header h6 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.filter-body {
    padding: 1.5rem;
    background: white;
}

/* 预设策略卡片 */
.preset-card {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    position: relative;
    overflow: hidden;
}

.preset-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.preset-card:hover {
    border-color: #007bff;
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
    transform: translateY(-3px);
}

.preset-card:hover::before {
    transform: scaleX(1);
}

.preset-card.active {
    border-color: #007bff;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
    box-shadow: 0 8px 25px rgba(0,123,255,0.2);
}

.preset-card.active::before {
    transform: scaleX(1);
}

.preset-icon {
    font-size: 2.5rem;
    margin-bottom: 0.75rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.preset-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.preset-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 输入框样式 */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
}

.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background: #17a2b8;
    border-color: #17a2b8;
}

/* 表格样式 */
.table-card {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.table-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
}

.table-header h6 {
    margin: 0;
    color: white;
    font-weight: 600;
}

.table-responsive {
    border-radius: 0 0 12px 12px;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem 0;
    }

    .filter-body {
        padding: 1rem;
    }

    .preset-card {
        padding: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.filter-card {
    animation: fadeInUp 0.5s ease-out;
}

.filter-card:nth-child(2) {
    animation-delay: 0.1s;
}

.filter-card:nth-child(3) {
    animation-delay: 0.2s;
}

.filter-card:nth-child(4) {
    animation-delay: 0.3s;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block title %}智能选股筛选器{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col">
                    <h2 class="page-title">
                        <i class="ti ti-filter me-2"></i>智能选股筛选器
                    </h2>
                    <div class="page-subtitle">
                        多维度财务指标筛选，发现优质投资标的
                    </div>
                </div>
                <div class="col-auto">
                    <div class="btn-list">
                        <button type="button" class="btn btn-outline-light" data-bs-toggle="modal" data-bs-target="#presetModal">
                            <i class="ti ti-template me-1"></i>预设策略
                        </button>
                        <button type="button" class="btn btn-outline-light" data-bs-toggle="modal" data-bs-target="#helpModal">
                            <i class="ti ti-help me-1"></i>指标说明
                        </button>
                        <button type="button" class="btn btn-light" onclick="document.getElementById('screenerForm').submit()">
                            <i class="ti ti-search me-1"></i>开始筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3" id="screenerForm">
                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-filter me-1"></i>基本筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- 行业选择 -->
                                    <div class="col-md-3">
                                        <label for="industry" class="form-label small">行业</label>
                                        <select class="form-select form-select-sm" id="industry" name="industry">
                                            <option value="">全部行业</option>
                                            {% for ind in industries %}
                                            <option value="{{ ind }}" {% if ind == filters.industry %}selected{% endif %}>{{ ind }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <!-- 市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">市值范围（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="market_cap_min" placeholder="最小值" value="{{ filters.market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="market_cap_max" placeholder="最大值" value="{{ filters.market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- 流通市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">流通市值（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="float_market_cap_min" placeholder="最小值" value="{{ filters.float_market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="float_market_cap_max" placeholder="最大值" value="{{ filters.float_market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- PE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PE范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pe_min" placeholder="最小值" value="{{ filters.pe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pe_max" placeholder="最大值" value="{{ filters.pe_max }}">
                                        </div>
                                    </div>

                                    <!-- PB范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PB范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pb_min" placeholder="最小值" value="{{ filters.pb_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pb_max" placeholder="最大值" value="{{ filters.pb_max }}">
                                        </div>
                                    </div>

                                    <!-- 搜索框 -->
                                    <div class="col-md-3">
                                        <label for="search" class="form-label small">搜索股票代码/名称</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="ti ti-search"></i></span>
                                            <input type="text" class="form-control" id="search" name="search" value="{{ filters.search }}" placeholder="输入股票代码或名称">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-chart-bar me-1"></i>财务指标筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- ROE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">ROE范围（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="roe_min" placeholder="最小值" value="{{ filters.roe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="roe_max" placeholder="最大值" value="{{ filters.roe_max }}">
                                        </div>
                                    </div>

                                    <!-- 净利润增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">净利润增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="net_profit_growth_min" placeholder="最小值" value="{{ filters.net_profit_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="net_profit_growth_max" placeholder="最大值" value="{{ filters.net_profit_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 营收增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">营收增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="revenue_growth_min" placeholder="最小值" value="{{ filters.revenue_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="revenue_growth_max" placeholder="最大值" value="{{ filters.revenue_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 毛利率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">毛利率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="gross_profit_margin_min" placeholder="最小值" value="{{ filters.gross_profit_margin_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="gross_profit_margin_max" placeholder="最大值" value="{{ filters.gross_profit_margin_max }}">
                                        </div>
                                    </div>

                                    <!-- 负债率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">负债率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="debt_ratio_min" placeholder="最小值" value="{{ filters.debt_ratio_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="debt_ratio_max" placeholder="最大值" value="{{ filters.debt_ratio_max }}">
                                        </div>
                                    </div>

                                    <!-- ROA范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">ROA范围（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="roa_min" placeholder="最小值" value="{{ filters.roa_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="roa_max" placeholder="最大值" value="{{ filters.roa_max }}">
                                        </div>
                                    </div>

                                    <!-- 净利率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">净利率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="net_profit_margin_min" placeholder="最小值" value="{{ filters.net_profit_margin_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="net_profit_margin_max" placeholder="最大值" value="{{ filters.net_profit_margin_max }}">
                                        </div>
                                    </div>

                                    <!-- 流动比率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">流动比率</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="current_ratio_min" placeholder="最小值" value="{{ filters.current_ratio_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="current_ratio_max" placeholder="最大值" value="{{ filters.current_ratio_max }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-star me-1"></i>高级筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- 连续3年ROE > 15% -->
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="continuous_roe" name="continuous_roe" value="1" {% if filters.continuous_roe %}checked{% endif %}>
                                            <label class="form-check-label small" for="continuous_roe">连续3年ROE > 15%</label>
                                        </div>
                                    </div>

                                    <!-- 连续增长 -->
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="continuous_growth" name="continuous_growth" value="1" {% if filters.continuous_growth %}checked{% endif %}>
                                            <label class="form-check-label small" for="continuous_growth">连续3年营收增长</label>
                                        </div>
                                    </div>

                                    <!-- 分红年数 -->
                                    <div class="col-md-4">
                                        <label class="form-label small">连续分红年数</label>
                                        <select class="form-select form-select-sm" name="dividend_years">
                                            <option value="">不限制</option>
                                            <option value="3" {% if filters.dividend_years == "3" %}selected{% endif %}>3年以上</option>
                                            <option value="5" {% if filters.dividend_years == "5" %}selected{% endif %}>5年以上</option>
                                            <option value="10" {% if filters.dividend_years == "10" %}selected{% endif %}>10年以上</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮组 -->
                        <div class="col-12 text-center mt-2 mb-2">
                            <button type="submit" class="btn btn-primary btn-sm px-3">
                                <i class="ti ti-search me-1"></i>开始筛选
                            </button>
                            <a href="{% url 'financial_analysis:financial_screener' %}" class="btn btn-outline-secondary btn-sm px-3 ms-2">
                                <i class="ti ti-refresh me-1"></i>重置条件
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 股票列表 -->
            <div class="card mt-4 shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center py-2">
                    <div class="d-flex align-items-center">
                        <i class="ti ti-table me-1"></i>
                        <h6 class="card-title mb-0">筛选结果</h6>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="badge bg-white text-primary me-2">
                            <i class="ti ti-chart-bar me-1"></i>
                            {{ page_obj.paginator.count }}支
                        </div>
                        <div class="text-white small me-2" style="font-size: 0.75rem;">
                            <i class="ti ti-calendar me-1"></i>
                            {{ latest_trade_date|date:"Y-m-d" }}
                        </div>
                        <div class="text-white small" style="font-size: 0.75rem;">
                            <i class="ti ti-report-money me-1"></i>
                            {{ latest_report_date|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
                <div class="card-body p-2">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped table-sm">
                            <thead class="table-light">
                                <tr style="font-size: 0.75rem;">
                                    <th width="40px">收藏</th>
                                    <th><a href="?sort=stock_code&order={% if sort_by == 'stock_code' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">代码 <i class="sort-icon {% if sort_by == 'stock_code' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=stock_name&order={% if sort_by == 'stock_name' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">名称 <i class="sort-icon {% if sort_by == 'stock_name' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=industry&order={% if sort_by == 'industry' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业 <i class="sort-icon {% if sort_by == 'industry' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=market_cap&order={% if sort_by == 'market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">市值(亿) <i class="sort-icon {% if sort_by == 'market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=pe_ratio&order={% if sort_by == 'pe_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PE(倍) <i class="sort-icon {% if sort_by == 'pe_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=industry_avg_pe&order={% if sort_by == 'industry_avg_pe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业PE(倍) <i class="sort-icon {% if sort_by == 'industry_avg_pe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=pb_ratio&order={% if sort_by == 'pb_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PB(倍) <i class="sort-icon {% if sort_by == 'pb_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=latest_price&order={% if sort_by == 'latest_price' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">当前价(元) <i class="sort-icon {% if sort_by == 'latest_price' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=change_percent&order={% if sort_by == 'change_percent' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">涨跌幅(%) <i class="sort-icon {% if sort_by == 'change_percent' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=roa&order={% if sort_by == 'roa' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">ROA(%) <i class="sort-icon {% if sort_by == 'roa' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=net_profit_margin&order={% if sort_by == 'net_profit_margin' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">净利率(%) <i class="sort-icon {% if sort_by == 'net_profit_margin' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=roe&order={% if sort_by == 'roe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">ROE(%) <i class="sort-icon {% if sort_by == 'roe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=net_profit_growth&order={% if sort_by == 'net_profit_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">净利润增(%) <i class="sort-icon {% if sort_by == 'net_profit_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=revenue_growth&order={% if sort_by == 'revenue_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">营收增(%) <i class="sort-icon {% if sort_by == 'revenue_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=gross_profit_margin&order={% if sort_by == 'gross_profit_margin' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">毛利率(%) <i class="sort-icon {% if sort_by == 'gross_profit_margin' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=debt_ratio&order={% if sort_by == 'debt_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">负债率(%) <i class="sort-icon {% if sort_by == 'debt_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=float_market_cap&order={% if sort_by == 'float_market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">流通市值(亿) <i class="sort-icon {% if sort_by == 'float_market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th>3年ROE>15%</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in page_obj %}
                                <tr class="align-middle" style="font-size: 0.75rem;">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <button class="btn btn-sm btn-warning favorite-btn"
                                                    data-code="{{ stock.stock_code }}"
                                                    data-name="{{ stock.stock_name }}"
                                                    title="添加到收藏">
                                                <i class="ti ti-star"></i>
                                            </button>
                                            <span class="favorite-icon ms-1 d-none" data-code="{{ stock.stock_code }}">
                                                <i class="ti ti-star-filled text-warning" style="font-size: 0.8rem;"></i>
                                            </span>
                                        </div>
                                    </td>
                                    <td><a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-primary fw-semibold">{{ stock.stock_code }}</a></td>
                                    <td><span class="fw-medium">{{ stock.stock_name }}</span></td>
                                    <td><span class="badge bg-light-info text-info rounded-pill px-1" style="font-size: 0.7rem;">{{ stock.industry }}</span></td>
                                    <td>{% if stock.market_cap %}{{ stock.market_cap|floatformat:1 }}亿{% else %}-{% endif %}</td>
                                    <td>{% if stock.pe_ratio %}{{ stock.pe_ratio|floatformat:1 }}倍{% else %}-{% endif %}</td>
                                    <td>
                                        {% if stock.industry_avg_pe %}
                                            {% if stock.pe_ratio %}
                                                {% with pe_ratio=stock.pe_ratio|floatformat:1|stringformat:"s" industry_pe=stock.industry_avg_pe|floatformat:1|stringformat:"s" %}
                                                    {% if stock.pe_ratio > stock.industry_avg_pe %}
                                                        <span class="badge bg-warning-subtle text-warning" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% elif stock.pe_ratio < stock.industry_avg_pe %}
                                                        <span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% endif %}
                                                {% endwith %}
                                            {% else %}
                                                <span class="text-muted">{{ stock.industry_avg_pe|floatformat:1 }}倍</span>
                                            {% endif %}
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>{% if stock.pb_ratio %}{{ stock.pb_ratio|floatformat:1 }}倍{% else %}-{% endif %}</td>
                                    <td>
                                        {% if stock.latest_price %}
                                            <span class="fw-medium">{{ stock.latest_price|floatformat:2 }}元</span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.change_percent != None %}
                                            <span class="badge {% if stock.change_percent > 0 %}bg-danger-subtle text-danger{% elif stock.change_percent < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:2 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.roa != None %}
                                            <span class="fw-medium {% if stock.roa > 0 %}text-danger{% else %}text-success{% endif %}">
                                                {{ stock.roa|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.net_profit_margin != None %}
                                            <span class="fw-medium {% if stock.net_profit_margin > 0 %}text-danger{% else %}text-success{% endif %}">
                                                {{ stock.net_profit_margin|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.roe != None %}
                                            <span class="fw-medium {% if stock.roe > 15 %}text-danger{% elif stock.roe > 8 %}text-warning{% else %}text-success{% endif %}">
                                                {{ stock.roe|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.net_profit_growth != None %}
                                            <span class="badge {% if stock.net_profit_growth > 0 %}bg-danger-subtle text-danger{% elif stock.net_profit_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.net_profit_growth > 0 %}+{% endif %}{{ stock.net_profit_growth|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.revenue_growth != None %}
                                            <span class="badge {% if stock.revenue_growth > 0 %}bg-danger-subtle text-danger{% elif stock.revenue_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.revenue_growth > 0 %}+{% endif %}{{ stock.revenue_growth|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.gross_profit_margin != None %}
                                            <span class="fw-medium {% if stock.gross_profit_margin > 30 %}text-danger{% elif stock.gross_profit_margin > 15 %}text-warning{% else %}text-muted{% endif %}">
                                                {{ stock.gross_profit_margin|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.debt_ratio != None %}
                                            <span class="fw-medium {% if stock.debt_ratio > 60 %}text-danger{% elif stock.debt_ratio > 40 %}text-warning{% else %}text-success{% endif %}">
                                                {{ stock.debt_ratio|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>{% if stock.float_market_cap != None %}{{ stock.float_market_cap|floatformat:1 }}亿{% else %}-{% endif %}</td>
                                    <td>{% if stock.continuous_roe %}<span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">是</span>{% else %}<span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">否</span>{% endif %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="19" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="ti ti-search-off fs-1 text-muted mb-3"></i>
                                            <p class="mb-1 fw-medium">没有找到符合条件的股票</p>
                                            <p class="text-muted small">请尝试调整筛选条件</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="mt-4">
                        {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 预设策略模态框 -->
<div class="modal modal-blur fade" id="presetModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-template me-2"></i>选择预设筛选策略
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    {% for key, preset in presets.items %}
                    <div class="col-md-6 mb-3">
                        <div class="preset-card" data-preset="{{ key }}">
                            <div class="d-flex align-items-center mb-2">
                                <i class="{{ preset.icon }} preset-icon me-2"></i>
                                <h6 class="mb-0">{{ preset.name }}</h6>
                            </div>
                            <p class="text-muted small mb-0">{{ preset.description }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <div class="alert alert-info">
                    <i class="ti ti-info-circle me-2"></i>
                    选择预设策略后，将自动填充相应的筛选条件。您可以在此基础上进一步调整参数。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="applyPreset()">应用策略</button>
            </div>
        </div>
    </div>
</div>

<!-- 指标说明模态框 -->
<div class="modal modal-blur fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ti ti-help me-2"></i>财务指标说明
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-chart-line me-2"></i>估值指标</h6>
                        <div class="mb-3">
                            <strong>PE (市盈率)</strong>
                            <p class="text-muted small mb-2">股价 ÷ 每股收益，反映投资回收期</p>
                            <span class="badge bg-success-subtle text-success">优秀: &lt;15</span>
                            <span class="badge bg-warning-subtle text-warning">一般: 15-30</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;30</span>
                        </div>
                        <div class="mb-3">
                            <strong>PB (市净率)</strong>
                            <p class="text-muted small mb-2">股价 ÷ 每股净资产，反映资产价值</p>
                            <span class="badge bg-success-subtle text-success">优秀: &lt;2</span>
                            <span class="badge bg-warning-subtle text-warning">一般: 2-5</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;5</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-trending-up me-2"></i>盈利能力</h6>
                        <div class="mb-3">
                            <strong>ROE (净资产收益率)</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 净资产，反映盈利能力</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;15%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 8-15%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;8%</span>
                        </div>
                        <div class="mb-3">
                            <strong>ROA (总资产收益率)</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 总资产，反映资产使用效率</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;5%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 2-5%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;2%</span>
                        </div>
                        <div class="mb-3">
                            <strong>净利率</strong>
                            <p class="text-muted small mb-2">净利润 ÷ 营业收入，反映盈利效率</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;10%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 5-10%</span>
                            <span class="badge bg-success-subtle text-success">一般: &lt;5%</span>
                        </div>
                        <div class="mb-3">
                            <strong>毛利率</strong>
                            <p class="text-muted small mb-2">毛利润 ÷ 营业收入，反映产品竞争力</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;30%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 15-30%</span>
                            <span class="badge bg-muted-subtle text-muted">一般: &lt;15%</span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-rocket me-2"></i>成长性指标</h6>
                        <div class="mb-3">
                            <strong>营收增长率</strong>
                            <p class="text-muted small mb-2">本期营收相比去年同期的增长幅度</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;20%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 10-20%</span>
                            <span class="badge bg-success-subtle text-success">下降: &lt;0%</span>
                        </div>
                        <div class="mb-3">
                            <strong>净利润增长率</strong>
                            <p class="text-muted small mb-2">本期净利润相比去年同期的增长幅度</p>
                            <span class="badge bg-danger-subtle text-danger">优秀: &gt;25%</span>
                            <span class="badge bg-warning-subtle text-warning">良好: 10-25%</span>
                            <span class="badge bg-success-subtle text-success">下降: &lt;0%</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-primary mb-3"><i class="ti ti-shield-check me-2"></i>财务健康度</h6>
                        <div class="mb-3">
                            <strong>资产负债率</strong>
                            <p class="text-muted small mb-2">总负债 ÷ 总资产，反映财务风险</p>
                            <span class="badge bg-success-subtle text-success">安全: &lt;40%</span>
                            <span class="badge bg-warning-subtle text-warning">适中: 40-60%</span>
                            <span class="badge bg-danger-subtle text-danger">偏高: &gt;60%</span>
                        </div>
                        <div class="mb-3">
                            <strong>流动比率</strong>
                            <p class="text-muted small mb-2">流动资产 ÷ 流动负债，反映短期偿债能力</p>
                            <span class="badge bg-success-subtle text-success">安全: &gt;2</span>
                            <span class="badge bg-warning-subtle text-warning">适中: 1-2</span>
                            <span class="badge bg-danger-subtle text-danger">偏低: &lt;1</span>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="ti ti-info-circle me-2"></i>
                    <strong>颜色说明：</strong>
                    <span class="text-danger">红色</span>表示上涨/优秀，
                    <span class="text-success">绿色</span>表示下跌/较差，
                    <span class="text-warning">黄色</span>表示中等水平。
                    具体标准因行业而异，请结合行业特点分析。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .sort-link {
                color: inherit;
                text-decoration: none;
                cursor: pointer;
                width: 100%;
                display: inline-flex;
                align-items: center;
                justify-content: space-between;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                transition: background-color 0.2s;
            }
            .sort-link:hover { color: #0d6efd; background-color: rgba(13, 110, 253, 0.05); }
            .sort-icon { margin-left: 0.5rem; opacity: 0.5; transition: opacity 0.2s; }
            .sort-link:hover .sort-icon { opacity: 1; }
            th {
                white-space: nowrap;
                transition: background-color 0.3s;
                padding: 0.4rem 0.5rem !important;
                font-weight: 500;
            }
            td { padding: 0.3rem 0.5rem !important; }
            th:hover { background-color: rgba(13, 110, 253, 0.05); }
            .bg-light-primary { background-color: rgba(13, 110, 253, 0.1); }
            .bg-light-info { background-color: rgba(13, 202, 240, 0.1); }
            .rounded-pill { border-radius: 50rem; }
            .table-sm { font-size: 0.75rem; }
            .card-body.p-2 .table { margin-bottom: 0.5rem; }
            .favorite-icon { cursor: pointer; }
            .favorite-icon.active { display: inline-block !important; }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(25, 135, 84, 0); }
                100% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0); }
            }
            .pulse-animation { animation: pulse 1.5s infinite; }
            .favorite-btn.disabled { opacity: 0.6; cursor: not-allowed; }
        `;
        document.head.appendChild(style);

        // 元素引用
        const favoriteIcons = document.querySelectorAll('.favorite-icon');
        const favoriteBtns = document.querySelectorAll('.favorite-btn');

        // 已收藏的股票列表
        let favoriteStocks = [];

        // 显示通知
        function showNotification(type, title, message) {
            const notification = document.createElement('div');
            notification.className = 'toast show position-fixed top-0 end-0 m-3';
            notification.style.zIndex = '9999';
            notification.innerHTML = `
                <div class="toast-header bg-${type} text-white">
                    <i class="ti ti-${type === 'success' ? 'star' : 'alert-triangle'} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 500);
            }, 3000);
        }

        // 获取已收藏的股票
        function loadFavoriteStocks() {
            fetch('{% url "financial_analysis:get_favorite_stocks" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        favoriteStocks = data.favorites;
                        updateFavoriteIcons();
                        updateFavoriteButtons();
                    }
                })
                .catch(error => {
                    console.error('Error loading favorites:', error);
                });
        }

        // 更新收藏图标显示
        function updateFavoriteIcons() {
            favoriteIcons.forEach(icon => {
                const stockCode = icon.dataset.code;
                if (favoriteStocks.includes(stockCode)) {
                    icon.classList.remove('d-none');
                    icon.classList.add('active');
                } else {
                    icon.classList.add('d-none');
                    icon.classList.remove('active');
                }
            });
        }

        // 更新收藏按钮状态
        function updateFavoriteButtons() {
            favoriteBtns.forEach(btn => {
                const stockCode = btn.dataset.code;
                if (favoriteStocks.includes(stockCode)) {
                    btn.classList.remove('btn-warning');
                    btn.classList.add('btn-success');
                    btn.setAttribute('title', '已收藏');
                    btn.disabled = true;
                    btn.classList.add('disabled');
                } else {
                    btn.classList.add('btn-warning');
                    btn.classList.remove('btn-success');
                    btn.setAttribute('title', '添加到收藏');
                    btn.disabled = false;
                    btn.classList.remove('disabled');
                }
            });
        }

        // 添加收藏按钮点击事件
        favoriteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const stockCode = this.dataset.code;
                const stockName = this.dataset.name;

                // 如果已经收藏过，则不再处理
                if (favoriteStocks.includes(stockCode)) {
                    return;
                }

                // 显示加载状态
                this.innerHTML = '<i class="ti ti-loader ti-spin"></i>';
                this.disabled = true;

                // 发送AJAX请求
                fetch('{% url "financial_analysis:ajax_add_favorite" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        stock_name: stockName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 成功处理
                        showNotification('success', '收藏成功', data.message);

                        // 如果是新添加的收藏，则更新收藏列表
                        if (data.created && !favoriteStocks.includes(data.stock_code)) {
                            favoriteStocks.push(data.stock_code);
                        }

                        // 更新UI
                        updateFavoriteIcons();
                        updateFavoriteButtons();
                    } else {
                        // 失败处理
                        showNotification('danger', '收藏失败', data.message);
                        this.innerHTML = '<i class="ti ti-star"></i>';
                        this.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('danger', '收藏失败', '网络错误，请稍后再试');
                    this.innerHTML = '<i class="ti ti-star"></i>';
                    this.disabled = false;
                });
            });
        });

        // 初始化
        loadFavoriteStocks();
    });

    // 预设策略相关功能
    let selectedPreset = null;

    // 预设策略选择
    document.querySelectorAll('.preset-card').forEach(card => {
        card.addEventListener('click', function() {
            document.querySelectorAll('.preset-card').forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            selectedPreset = this.dataset.preset;
        });
    });

    // 应用预设策略
    function applyPreset() {
        if (!selectedPreset) {
            alert('请先选择一个预设策略');
            return;
        }

        const presets = {
            'value_stocks': {
                'pe_max': '15',
                'pb_max': '2',
                'roe_min': '10',
                'debt_ratio_max': '50'
            },
            'growth_stocks': {
                'revenue_growth_min': '20',
                'net_profit_growth_min': '25',
                'roe_min': '15',
                'pe_max': '30'
            },
            'quality_stocks': {
                'continuous_roe': '1',
                'roe_min': '15',
                'debt_ratio_max': '40'
            },
            'dividend_stocks': {
                'roe_min': '8',
                'debt_ratio_max': '60',
                'pe_max': '20'
            },
            'small_cap_growth': {
                'market_cap_max': '100',
                'revenue_growth_min': '30',
                'net_profit_growth_min': '35',
                'roe_min': '20'
            },
            'undervalued_stocks': {
                'pe_max': '12',
                'pb_max': '1.5',
                'roe_min': '12'
            }
        };

        const preset = presets[selectedPreset];
        if (preset) {
            // 清空现有条件
            document.getElementById('screenerForm').reset();

            // 应用预设条件
            for (const [key, value] of Object.entries(preset)) {
                const input = document.querySelector(`[name="${key}"]`);
                if (input) {
                    if (input.type === 'checkbox') {
                        input.checked = value === '1';
                    } else {
                        input.value = value;
                    }
                }
            }

            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('presetModal'));
            modal.hide();

            // 显示成功提示
            const notification = document.createElement('div');
            notification.className = 'toast show position-fixed top-0 end-0 m-3';
            notification.style.zIndex = '9999';
            notification.innerHTML = `
                <div class="toast-header bg-success text-white">
                    <i class="ti ti-check me-2"></i>
                    <strong class="me-auto">策略已应用</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">预设策略条件已自动填充，您可以进一步调整后开始筛选。</div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 500);
            }, 3000);
        }
    }
</script>
{% endblock %}